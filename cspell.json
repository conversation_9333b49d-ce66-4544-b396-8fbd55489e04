// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON>sen",
    "Antialiasing",
    "autogen",
    "AUTOMOC",
    "AUTORCC",
    "AUTOSAR",
    "AUTOUIC",
    "axisartist",
    "Axisd",
    "axvspan",
    "bbft",
    "bbfv",
    "BOCIC",
    "bottops",
    "Bpearl",
    "bugprone",
    "CALIB",
    "Cbus",
    "centralwidget",
    "chardet",
    "chisq",
    "CICD",
    "combobox",
    "controll",
    "cpack",
    "decomp",
    "deno",
    "denois",
    "dialout",
    "DIFOP",
    "dropna",
    "DRSFSCLOG",
    "dspinbox",
    "dtags",
    "duts",
    "Eigen",
    "elimcode",
    "elio",
    "Emissing",
    "enduml",
    "figsize",
    "fitoptions",
    "fontsize",
    "fontweight",
    "Fout",
    "FUNCSIG",
    "fusa",
    "gbit",
    "gedit",
    "ggdb",
    "gitsubmodule",
    "glnxa",
    "gmock",
    "googletest",
    "groupbox",
    "GSLCBLAS",
    "gtest",
    "hhmmss",
    "hicpp",
    "intrin",
    "killall",
    "Lennox",
    "lfsr",
    "Liang",
    "libmems",
    "libqt",
    "librsfsc",
    "linalg",
    "lineedit",
    "loguru",
    "LPTOP",
    "lsnormal",
    "lsusb",
    "mainwindow",
    "markersize",
    "matplotlibcpp",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "METATYPE",
    "mseries",
    "MSOP",
    "mticker",
    "multifit",
    "multimediawidgets",
    "munubar",
    "MYAPPICON",
    "nbreak",
    "ncoeffs",
    "ncontrol",
    "nderiv",
    "newjingyanquxian",
    "NOLINT",
    "NOLINTNEXTLINE",
    "nqle",
    "opencv",
    "OPENMP",
    "oprod",
    "pcap",
    "pcba",
    "Pixmap",
    "pktcnt",
    "Plantuml",
    "puml",
    "qiandao",
    "QMESSAGE",
    "qobject",
    "qreal",
    "qsetting",
    "qsettings",
    "QSQLITE",
    "qstyleoption",
    "qtmultimedia",
    "Quaterniond",
    "refcal",
    "rheight",
    "rmse",
    "robosense",
    "rosnode",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "rwidth",
    "Shen",
    "SHIYAN",
    "smoothingspline",
    "Sout",
    "spdlog",
    "startuml",
    "stdstr",
    "suteng",
    "tablewidget",
    "tabwidget",
    "thorlabs",
    "topboard",
    "toptobot",
    "tparam",
    "tpng",
    "unitless",
    "usbtmc",
    "utest",
    "VBavg",
    "VBpeak",
    "vbus",
    "vccaux",
    "vccint",
    "Wconversion",
    "Wcppcoreguidelines",
    "Werror",
    "Wextra",
    "widgetaction",
    "wlinear",
    "Wpedantic",
    "xlabel",
    "xlim",
    "xytext",
    "YAMLCPP",
    "Ying",
    "ylabel",
    "ylim",
    "Zhang",
    "ZHONG",
    "zorder"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
