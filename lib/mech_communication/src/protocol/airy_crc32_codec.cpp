﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/airy_crc32_codec.h"
#include "mech_communication/protocol/data_struct/airy.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/crc.hpp>
#include <boost/endian/conversion.hpp>

using CustomCrc32 = boost::crc_optimal<32,          // CRC 宽度
                                       0xF4ACFB13,  // 自定义多项式
                                       0x00000000,  // 初始寄存器值（InitRem）
                                       0x00000000,  // 输出异或值（XorOut）
                                       false,       // 输入不反转（ReflectIn）
                                       false        // 输出不反转（ReflectOut）
                                       >;

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

using CheckSumType = uint32_t;
namespace mech
{
constexpr uint32_t FRAME_WRAP_SIZE = sizeof(mech::FrameHead) - sizeof(mech::FrameHead::cmd_type) -
                                     sizeof(mech::FrameHead::response_type) + sizeof(FRAME_TAIL_FLAG) +
                                     sizeof(CheckSumType);
namespace server
{
constexpr uint32_t FRAME_WRAP_SIZE =
  sizeof(server::FrameHead) - sizeof(mech::FrameHead::cmd_type) + sizeof(FRAME_TAIL_FLAG) + sizeof(CheckSumType);
}  // namespace server
}  // namespace mech

AiryCrc32Codec::AiryCrc32Codec() = default;

std::string AiryCrc32Codec::getCurrentProtocolType() { return "AiryCrc32"; }
std::vector<uint8_t> AiryCrc32Codec::frameTailPack(std::vector<uint8_t>& _frame_array)
{  // notice that it is necessary to include the tail byte and frame type of
  // the data frame for calculating checksum
  _frame_array.emplace_back(mech::FRAME_TAIL_FLAG);

  CustomCrc32 crc;
  crc.process_bytes(_frame_array.data(), _frame_array.size());
  uint32_t crc_value = crc.checksum();

  for (int i = 3; i >= 0; i--)
  {
    _frame_array.emplace_back((crc_value >> (8U * i)) & 0xFFU);
  }

  return _frame_array;
}
bool AiryCrc32Codec::parseClientPacket(std::vector<uint8_t>& _packet_buffer)
{
  if (_packet_buffer.size() < sizeof(mech::FrameHead))
  {
    error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
    return false;
  }

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  auto* frame_head = reinterpret_cast<mech::FrameHead*>(_packet_buffer.data());

  while (frame_head->frame_header == mech::FRAME_FLAG)
  {
    auto payload_size = boost::endian::big_to_native(frame_head->length);

    auto expected_data_size = payload_size + mech::FRAME_WRAP_SIZE;
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(CheckSumType);
    CustomCrc32 crc;
    crc.process_bytes(_packet_buffer.data(), check_sum_data_size);
    auto cal_check_sum = crc.checksum();

    CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(CheckSumType)), sizeof(CheckSumType));
    check_sum = boost::endian::big_to_native(check_sum);

    if (cal_check_sum != check_sum)
    {
      error_str = fmt::format("crc32 error，expect: {:#x}, actual: {:#x}", cal_check_sum, check_sum);
      _packet_buffer.clear();
      return false;
    }

    mech::ResponseType response_type = static_cast<mech::ResponseType>(frame_head->response_type);
    switch (response_type)
    {
    case mech::ResponseType::SUCCESS:
    {
      error_str = "";
      break;
    }
    case mech::ResponseType::UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 不支持该指令";
      break;
    }
    case mech::ResponseType::PARAMETER_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 参数错误";
      break;
    }
    case mech::ResponseType::DATA_LEN_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 数据长度错误";
      break;
    }
    case mech::ResponseType::FORMAT_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 格式错误";
      break;
    }
    case mech::ResponseType::CHECKSUM_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 校验和错误";
      break;
    }
    case mech::ResponseType::OTHER:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 其他错误";
      break;
    }
    case mech::ResponseType::TIMEOUT:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 超时错误";
      break;
    }
    case mech::ResponseType::CURRENT_STATUS:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 当前状态错误";
      break;
    }
    case mech::ResponseType::VERSION_UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 版本不支持";
      break;
    }
    default:
    {
      error_str = fmt::format("错误码: {}", static_cast<uint32_t>(response_type));
      break;
    }
    }
    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(CheckSumType));
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(boost::endian::big_to_native(frame_head->cmd_type), PAYLOAD);
    }

    if (response_type != mech::ResponseType::SUCCESS)
    {
      return false;
    }
    if (_packet_buffer.size() < sizeof(mech::FrameHead))
    {
      break;
    }
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    frame_head = reinterpret_cast<mech::FrameHead*>(_packet_buffer.data());
  }

  return true;
}
bool AiryCrc32Codec::parseServerPacket(std::vector<uint8_t>& _packet_buffer)
{
  if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
  {
    error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
    return false;
  }

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  auto* frame_head = reinterpret_cast<mech::server::FrameHead*>(_packet_buffer.data());
  while (frame_head->frame_header == mech::FRAME_FLAG || _packet_buffer.size() > sizeof(mech::server::FrameHead))
  {
    if (frame_head->frame_header != mech::FRAME_FLAG)
    {
      // 不是帧头，提示错误，并继续找到帧头
      error_str =
        fmt::format("frame header error, expect: {:#x}, actual: {:#x}", mech::FRAME_FLAG, frame_head->frame_header);
      _packet_buffer.clear();
      return false;
    }
    auto payload_size = boost::endian::big_to_native(frame_head->length);

    auto expected_data_size = payload_size + mech::server::FRAME_WRAP_SIZE;
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(CheckSumType);
    CustomCrc32 crc;
    crc.process_bytes(_packet_buffer.data(), check_sum_data_size);
    auto cal_check_sum = crc.checksum();

    CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(CheckSumType)), sizeof(CheckSumType));
    check_sum = boost::endian::big_to_native(check_sum);

    if (cal_check_sum != check_sum)
    {
      // error_str = "crc error, expect: " + hex(cal_check_sum) + ", actual: " + hex(check_sum);
      error_str = fmt::format("crc32 error，expect: {:#x}, actual: {:#x}", cal_check_sum, check_sum);
      _packet_buffer.clear();
      return false;
    }

    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::server::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(CheckSumType));
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(boost::endian::big_to_native(frame_head->cmd_type), PAYLOAD);
    }

    if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
    {
      break;
    }
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    frame_head = reinterpret_cast<mech::server::FrameHead*>(_packet_buffer.data());
  }

  return true;
}

}  // namespace lidar
}  // namespace robosense
