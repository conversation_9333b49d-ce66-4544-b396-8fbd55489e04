﻿/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/widgets/mech_comm_widget.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QComboBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QMessageBox>
#include <QPlainTextEdit>
#include <QPushButton>
#include <QScrollArea>
#include <QSpinBox>
#include <QVBoxLayout>

// NOLINTNEXTLINE
namespace robosense
{
namespace lidar
{

MechCommWidget::MechCommWidget(QWidget* _parent) : QWidget(_parent)
{
  setupUi();
  createConnections();
  ptr_mech_comm_ = std::make_unique<MechCommunication>(combo_project_code_->currentText().toStdString());
}

void MechCommWidget::setupUi()
{
  auto* main_layout = new QVBoxLayout(this);

  setupLidarTypeSection(main_layout);
  setupConnectionSection(main_layout);
  setupOperationSection(main_layout);
}

void MechCommWidget::setupLidarTypeSection(QVBoxLayout* _main_layout)
{
  auto* type_layout     = new QHBoxLayout;
  auto* label_type      = new QLabel(u8"雷达类型:", this);
  combo_box_lidar_type_ = new QComboBox(this);
  combo_project_code_   = new QComboBox(this);

  std::vector<QString> lidar_types   = { "Helios", "BPearl", "Ruby", u8"Ruby老协议", "Airy", "AiryCRC32" };
  std::vector<QString> project_codes = { "0350", "0351", "0352" };

  for (const auto& type_name : lidar_types)
  {
    combo_box_lidar_type_->addItem(type_name);
  }
  for (const auto& project_code : project_codes)
  {
    combo_project_code_->addItem(project_code);
  }

  type_layout->addWidget(label_type);
  type_layout->addWidget(combo_box_lidar_type_);
  type_layout->addWidget(new QLabel(u8"项目编号:", this));
  type_layout->addWidget(combo_project_code_);
  type_layout->addStretch();

  _main_layout->addLayout(type_layout);
}

void MechCommWidget::setupConnectionSection(QVBoxLayout* _main_layout)
{
  auto* connect_layout = new QHBoxLayout;

  // IP设置
  auto* label_ip = new QLabel("IP:", this);
  line_edit_ip_  = new QLineEdit("*************", this);

  // MSOP端口设置
  auto* label_msop    = new QLabel("MSOP:", this);
  spin_box_msop_port_ = new QSpinBox(this);
  spin_box_msop_port_->setRange(0, 115200);
  spin_box_msop_port_->setValue(6699);

  // 按钮设置
  button_connect_  = new QPushButton(u8"连接雷达", this);
  button_ping_     = new QPushButton(u8"ping测试", this);
  button_eye_safe_ = new QPushButton(u8"打开人眼安全", this);

  connect_layout->addWidget(label_ip);
  connect_layout->addWidget(line_edit_ip_);
  connect_layout->addWidget(label_msop);
  connect_layout->addWidget(spin_box_msop_port_);
  connect_layout->addWidget(button_connect_);
  connect_layout->addWidget(button_ping_);
  connect_layout->addWidget(button_eye_safe_);

  _main_layout->addLayout(connect_layout);
}

void MechCommWidget::setupOperationSection(QVBoxLayout* _main_layout)
{
  widget_operation_ = new QWidget(this);
  widget_operation_->setEnabled(false);
  auto* operation_layout = new QVBoxLayout(widget_operation_);

  setupRegisterControls(operation_layout);
  setupRegisterList(operation_layout);

  _main_layout->addWidget(widget_operation_);
}

void MechCommWidget::setupRegisterControls(QVBoxLayout* _operation_layout)
{
  auto* control_layout = new QHBoxLayout;

  auto* label_count   = new QLabel(u8"寄存器数量:", this);
  spin_box_reg_count_ = new QSpinBox(this);
  spin_box_reg_count_->setRange(1, 20);
  spin_box_reg_count_->setValue(5);

  button_multi_read_  = new QPushButton(u8"批量读取", this);
  button_multi_write_ = new QPushButton(u8"批量写入", this);

  control_layout->addWidget(label_count);
  control_layout->addWidget(spin_box_reg_count_);
  control_layout->addStretch();
  control_layout->addWidget(button_multi_read_);
  control_layout->addWidget(button_multi_write_);

  _operation_layout->addLayout(control_layout);
}

void MechCommWidget::setupRegisterList(QVBoxLayout* _operation_layout)
{
  scroll_area_ = new QScrollArea(this);
  scroll_area_->setWidgetResizable(true);

  widget_reg_values_ = createRegisterWidget(5);  // 默认5个寄存器
  scroll_area_->setWidget(widget_reg_values_);

  _operation_layout->addWidget(scroll_area_);
}

void MechCommWidget::createConnections()
{
  connect(combo_box_lidar_type_, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
          &MechCommWidget::onLidarTypeChanged);
  connect(button_connect_, &QPushButton::clicked, this, &MechCommWidget::onConnectButtonClicked);
  connect(button_ping_, &QPushButton::clicked, this, &MechCommWidget::onPingButtonClicked);
  connect(button_eye_safe_, &QPushButton::clicked, this, &MechCommWidget::onSetEyeSafeButtonClicked);
  connect(spin_box_reg_count_, QOverload<int>::of(&QSpinBox::valueChanged), this,
          &MechCommWidget::updateRegisterInputs);
  connect(button_multi_read_, &QPushButton::clicked, this, &MechCommWidget::onMultiRegReadClicked);
  connect(button_multi_write_, &QPushButton::clicked, this, &MechCommWidget::onMultiRegWriteClicked);
}

// 实现槽函数
void MechCommWidget::onLidarTypeChanged(int _index)
{
  if (ptr_mech_comm_ && ptr_mech_comm_->isConnected())
  {
    ptr_mech_comm_->disconnect();
    button_connect_->setText(u8"连接雷达");
    slotDisconnected();
  }

  ptr_mech_comm_ = std::make_unique<MechCommunication>(combo_project_code_->currentText().toStdString());
}

void MechCommWidget::onConnectButtonClicked()
{
  if (ptr_mech_comm_ && ptr_mech_comm_->isConnected())
  {
    ptr_mech_comm_->disconnect();
    button_connect_->setText(u8"连接雷达");
    slotDisconnected();
    return;
  }

  std::string ip_addr = line_edit_ip_->text().toStdString();
  uint32_t port       = spin_box_msop_port_->value();
  // ProtocolType lidar_type = static_cast<ProtocolType>(combo_box_lidar_type_->currentIndex());

  ptr_mech_comm_ = std::make_unique<MechCommunication>(combo_project_code_->currentText().toStdString());

  // if (ptr_mech_comm_->connect(ip_addr, port))
  if (ptr_mech_comm_->connect(SerialClientConfig { ip_addr, port }))
  {
    slotConnected();
    button_connect_->setText(u8"断开连接");
  }
}

void MechCommWidget::onPingButtonClicked()
{
  std::string ip_addr = line_edit_ip_->text().toStdString();
  if (MechCommunication::ping(ip_addr))
  {
    QMessageBox::information(this, u8"连接状态", u8"ping成功");
  }
  else
  {
    QMessageBox::critical(this, u8"连接超时", u8"ping超时100ms");
  }
}

void MechCommWidget::onSetEyeSafeButtonClicked()
{
  if (!ptr_mech_comm_ || !ptr_mech_comm_->isConnected())
  {
    QMessageBox::warning(this, u8"警告", u8"请先连接雷达");
    return;
  }

  bool is_open = button_eye_safe_->text() == u8"开启人眼安全";
  if (!ptr_mech_comm_->setEyesSafe(is_open))
  {
    QMessageBox::warning(this, u8"警告", u8"设置眼睛保护失败");
    return;
  }
  button_eye_safe_->setText(is_open ? u8"关闭人眼安全" : u8"开启人眼安全");
}

QWidget* MechCommWidget::createRegisterWidget(int _count)
{
  auto* container = new QWidget(scroll_area_);  // 设置正确的parent
  auto* layout    = new QVBoxLayout(container);

  reg_rows_.clear();

  // 创建新的行
  for (int i = 0; i < _count; ++i)
  {
    auto* row_layout = new QHBoxLayout;
    RegRowWidgets row_widgets;

    // 地址输入
    auto* label_addr      = new QLabel(QString(u8"地址 %1:").arg(i + 1), container);
    row_widgets.addr_edit = new QLineEdit(container);
    row_widgets.addr_edit->setPlaceholderText("0x...");

    // 写入值
    auto* label_write       = new QLabel(u8"写入值:", container);
    row_widgets.write_value = new QLineEdit(container);
    row_widgets.write_value->setPlaceholderText("0x...");

    // 读取值
    auto* label_read       = new QLabel(u8"读取值:", container);
    row_widgets.read_value = new QLineEdit(container);
    row_widgets.read_value->setReadOnly(true);

    // 按钮
    row_widgets.read_button  = new QPushButton(u8"读取", container);
    row_widgets.write_button = new QPushButton(u8"写入", container);

    row_layout->addWidget(label_addr);
    row_layout->addWidget(row_widgets.addr_edit);
    row_layout->addWidget(label_write);
    row_layout->addWidget(row_widgets.write_value);
    row_layout->addWidget(label_read);
    row_layout->addWidget(row_widgets.read_value);
    row_layout->addWidget(row_widgets.read_button);
    row_layout->addWidget(row_widgets.write_button);

    layout->addLayout(row_layout);
    reg_rows_.push_back(row_widgets);

    // 连接信号槽
    connect(row_widgets.read_button, &QPushButton::clicked, this, &MechCommWidget::onSingleRegReadClicked);
    connect(row_widgets.write_button, &QPushButton::clicked, this, &MechCommWidget::onSingleRegWriteClicked);
  }

  return container;
}

void MechCommWidget::updateRegisterInputs(int _count)
{
  // 创建新的widget
  QWidget* new_widget = createRegisterWidget(_count);

  // 替换原来的widget
  if (widget_reg_values_)
  {
    widget_reg_values_->hide();              // 先隐藏旧widget
    widget_reg_values_->setParent(nullptr);  // 移除父子关系
    widget_reg_values_->deleteLater();       // 安全删除
  }

  widget_reg_values_ = new_widget;
  widget_reg_values_->setParent(scroll_area_);  // 设置正确的父子关系
  scroll_area_->setWidget(widget_reg_values_);
  widget_reg_values_->show();  // 显示新widget
}

void MechCommWidget::onSingleRegReadClicked()
{
  auto* push_button = qobject_cast<QPushButton*>(sender());
  if (push_button == nullptr)
  {
    return;
  }

  // 找到对应的行
  auto iter = std::find_if(reg_rows_.begin(), reg_rows_.end(),
                           [push_button](const RegRowWidgets& _row) { return _row.read_button == push_button; });
  if (iter == reg_rows_.end())
  {
    return;
  }

  bool convert_ok  = false;
  uint32_t address = iter->addr_edit->text().toUInt(&convert_ok, 16);
  if (!convert_ok)
  {
    QMessageBox::warning(this, u8"警告", u8"地址格式错误");
    return;
  }

  uint32_t value = 0;
  if (ptr_mech_comm_->readRegData(address, value))
  {
    iter->read_value->setText(QString::fromStdString(fmt::format("0x{:08X}", value)));
    LOG_INFO("read reg: {:#x} -> {:#x}", address, value);
  }
  else
  {
    QMessageBox::warning(this, u8"警告", u8"读取失败");
  }
}

void MechCommWidget::onSingleRegWriteClicked()
{
  auto* button = qobject_cast<QPushButton*>(sender());
  if (button == nullptr)
  {
    return;
  }

  auto iter = std::find_if(reg_rows_.begin(), reg_rows_.end(),
                           [button](const RegRowWidgets& _row) { return _row.write_button == button; });
  if (iter == reg_rows_.end())
  {
    return;
  }

  bool convert_ok  = false;
  uint32_t address = iter->addr_edit->text().toUInt(&convert_ok, 16);
  if (!convert_ok)
  {
    QMessageBox::warning(this, u8"警告", u8"地址格式错误");
    return;
  }

  uint32_t value = iter->write_value->text().toUInt(&convert_ok, 16);
  if (!convert_ok)
  {
    QMessageBox::warning(this, u8"警告", u8"数值格式错误");
    return;
  }

  if (ptr_mech_comm_->writeRegData(address, value))
  {
    LOG_INFO("write reg: {:#x} -> {:#x}", address, value);
  }
  else
  {
    QMessageBox::warning(this, u8"警告", u8"写入失败");
  }
}

void MechCommWidget::onMultiRegReadClicked()
{
  std::vector<uint32_t> addresses;
  bool convert_ok = false;

  // 收集所有非空地址
  for (const auto& row : reg_rows_)
  {
    QString addr_text = row.addr_edit->text().trimmed();
    if (!addr_text.isEmpty())
    {
      uint32_t address = addr_text.toUInt(&convert_ok, 16);
      if (!convert_ok)
      {
        QMessageBox::warning(this, u8"警告", u8"地址格式错误");
        return;
      }
      addresses.push_back(address);
    }
  }

  if (addresses.empty())
  {
    QMessageBox::warning(this, u8"警告", u8"没有有效的地址");
    return;
  }

  std::vector<uint32_t> values;
  if (ptr_mech_comm_->readRegData(addresses, values))
  {
    // 更新对应行的读取值
    size_t value_index = 0;
    for (const auto& row : reg_rows_)
    {
      if (!row.addr_edit->text().trimmed().isEmpty() && value_index < values.size())
      {
        row.read_value->setText(QString::fromStdString(fmt::format("{:#x}", values[value_index])));
        ++value_index;
      }
    }
    LOG_INFO("read multi reg: [{:#x}] -> [{:#x}]", fmt::join(addresses, " "), fmt::join(values, " "));
  }
  else
  {
    QMessageBox::warning(this, u8"警告", u8"批量读取失败");
  }
}

void MechCommWidget::onMultiRegWriteClicked()
{
  std::vector<uint32_t> addresses;
  std::vector<uint32_t> values;
  bool convert_ok = false;

  // 收集所有非空的地址和值
  for (const auto& row : reg_rows_)
  {
    QString addr_text  = row.addr_edit->text().trimmed();
    QString value_text = row.write_value->text().trimmed();

    if (!addr_text.isEmpty() && !value_text.isEmpty())
    {
      uint32_t address = addr_text.toUInt(&convert_ok, 16);
      if (!convert_ok)
      {
        QMessageBox::warning(this, u8"警告", u8"地址格式错误");
        return;
      }

      uint32_t value = value_text.toUInt(&convert_ok, 16);
      if (!convert_ok)
      {
        QMessageBox::warning(this, u8"警告", u8"数值格式错误");
        return;
      }

      addresses.push_back(address);
      values.push_back(value);
    }
  }

  if (addresses.empty())
  {
    QMessageBox::warning(this, u8"警告", u8"没有有效的地址和值");
    return;
  }

  if (ptr_mech_comm_->writeRegData(addresses, values))
  {
    LOG_INFO("write multi reg: [{:#x}] -> [{:#x}]", fmt::join(addresses, " "), fmt::join(values, " "));
  }
  else
  {
    QMessageBox::warning(this, u8"警告", u8"批量写入失败");
  }
}

void MechCommWidget::slotConnected() { widget_operation_->setEnabled(true); }

void MechCommWidget::slotDisconnected() { widget_operation_->setEnabled(false); }

}  // namespace lidar
}  // namespace robosense