﻿{
  "comment": "其中 distance 为靶板中心的真值（单位：mm），width 为当前靶板的宽度，raw_dist_min 为未补偿动静标前筛选靶板的最小测距值，comped_dist_min 为补偿后的最小测距值(0.5cm)。反标靶板是在补偿动静标后筛选的，因此使用 comped_dist_min 作为筛选靶板的依据(0.5cm)",
  "HHL":{
    "board_info_map":{
      "1":{
        "distance": 1248,
        "width": 550,
        "comped_dist_min": 225,
        "comped_dist_max": 285,
        "board_start_angle": 14.5,
        "board_end_angle": 46.4,
        "refl_board_vec":[
          {"refl":255, "width": 110},
          {"refl":90, "width": 220},
          {"refl":40, "width": 110},
          {"refl":10, "width": 110}
        ]
      },
      "2":{
        "distance": 19766,
        "width": 5250,
        "raw_dist_min":4300,
        "raw_dist_max":4500,
        "board_start_angle":58,
        "board_end_angle":73.2,
        "refl_board_vec":[
          {"refl":10, "width": 1750, "distance": 19850},
          {"refl":40, "width": 1750, "distance": 19766},
          {"refl":90, "width": 1750, "distance": 19850}
        ]
      },
      "3":{
        "distance": 4999,
        "width": 1200,
        "raw_dist_min": 1300,
        "raw_dist_max": 1500,
        "board_start_angle":74.2,
        "board_end_angle":87.4,
        "refl_board_vec":[
          {"refl":2, "width": 200},
          {"refl":255, "width": 200},
          {"refl":90, "width": 200},
          {"refl":40, "width": 200},
          {"refl":10, "width": 200},
          {"refl":2, "width": 200}
        ]
      },
      "4":{
        "distance": 231,
        "width": 150,
        "comped_dist_min": 10,
        "comped_dist_max": 100,
        "board_start_angle":88.5,
        "board_end_angle":141,
        "refl_board_vec":[
          {"refl":10, "width": 30},
          {"refl":40, "width": 30},
          {"refl":90, "width": 60},
          {"refl":255, "width": 30}
        ]
      },
      "5":{
        "distance": 2995,
        "raw_dist_min": 900,
        "raw_dist_max": 1100,
        "board_start_angle":153.85,
        "board_end_angle":185.4,
        "width": 1550,
        "refl_board_vec":[
          {"refl":255, "width": 410},
          {"refl":90, "width": 520},
          {"refl":40, "width": 310},
          {"refl":10, "width": 310}
        ]
      },
      "6":{
        "distance": 1506,
        "width": 130,
        "comped_dist_min": 270,
        "comped_dist_max": 330,
        "board_start_angle":191,
        "board_end_angle":195,
        "refl_board_vec":[
          {"refl":90, "width": 130}
        ]
      },
      "7":{
        "distance": 610,
        "width": 100,
        "comped_dist_min": 90,
        "comped_dist_max": 150,
        "board_start_angle":202,
        "board_end_angle":213,
        "refl_board_vec":[
          {"refl":90, "width": 100}
        ]
      },
      "8":{
        "distance": 10083,
        "raw_dist_min": 2350,
        "raw_dist_max": 2550,
        "board_start_angle":222.5,
        "board_end_angle":249.5,
        "width": 5100,
        "refl_board_vec":[
          {"refl":10, "width": 1020,"distance": 10307},
          {"refl":40, "width": 1020,"distance": 10138},
          {"refl":90, "width": 2040,"distance": 10081},
          {"refl":255, "width": 1020,"distance": 10140}
        ]
      }
    },
    "refl_board_id_vec": [1, 2, 4, 5, 8],
    "abs_board_id_vec": [7, 1, 5, 3, 8, 2],
    "board_10m_id": 8,
    "board_20m_id": 2,
    "static_board_id": 5,
    "dynamic_board_id": 3
  },
  "SS":{
    "board_info_map":{
      "1":{
        "distance": 5014,
        "width": 1200,
        "raw_dist_min": 1300,
        "raw_dist_max": 1500,
        "comped_dist_min": 950,
        "comped_dist_max": 1100,
        "board_start_angle":2.87,
        "board_end_angle":18.67,
        "board_end_remove_factor": 0.0,
        "refl_board_vec":[
          {"refl":2, "width": 200, "distance": 5046},
          {"refl":255, "width": 200, "distance": 5026},
          {"refl":90, "width": 200, "distance": 5019},
          {"refl":40, "width": 200, "distance": 5019},
          {"refl":10, "width": 200, "distance": 5026},
          {"refl":2, "width": 200, "distance": 5040}
        ]
      },
      "2":{
        "distance": 19842,
        "width": 5250,
        "raw_dist_min":4000,
        "raw_dist_max":4500,
        "board_start_angle":23,
        "board_end_angle":37.8,
        "refl_board_vec":[
          {"refl":10, "width": 1750, "distance": 19890},
          {"refl":40, "width": 1750, "distance": 19842},
          {"refl":90, "width": 1750, "distance": 19946}
        ]
      },
      "3":{
        "distance": 3008,
        "width": 1550,
        "raw_dist_min": 890,
        "raw_dist_max": 1050,
        "board_start_angle":91,
        "board_end_angle":124,
        "refl_board_vec":[
          {"refl":10, "width": 310, "distance": 3089},
          {"refl":40, "width": 310, "distance": 3010},
          {"refl":90, "width": 620, "distance": 3029, "pos_factor": 0.5},
          {"refl":255, "width": 310, "distance": 3089}
        ]
      },
      "4": {
        "distance": 598,
        "width": 100,
        "comped_dist_min": 90,
        "comped_dist_max": 170,
        "board_start_angle":46,
        "board_end_angle":54,
        "refl_board_vec":[
          {"refl":90, "width": 100, "distance": 598}
        ]
      },
      "5":{
        "distance": 1501,
        "width": 130,
        "comped_dist_min": 271,
        "comped_dist_max": 331,
        "board_start_angle":62,
        "board_end_angle":72,
        "refl_board_vec":[
          {"refl":90, "width": 130, "distance": 1501}
        ]
      },
      "6":{
        "distance": 203,
        "width": 150,
        "comped_dist_min": 10,
        "comped_dist_max": 100,
        "board_start_angle":133.5,
        "board_end_angle":191,
        "refl_board_vec":[
          {"refl":255, "width": 30, "distance": 226},
          {"refl":90, "width": 60, "distance": 204, "pos_factor": 0.5},
          {"refl":40, "width": 30, "distance": 213},
          {"refl":10, "width": 30, "distance": 233}
        ]
      },
      "7":{
        "distance": 10018,
        "width": 5100,
        "raw_dist_min": 2300,
        "raw_dist_max": 2600,
        "board_start_angle":197.8,
        "board_end_angle":225,
        "refl_board_vec":[
          {"refl":255, "width": 1020, "distance": 10213},
          {"refl":90, "width": 2040, "distance": 10014, "pos_factor": 0.5},
          {"refl":40, "width": 1020, "distance": 10064},
          {"refl":10, "width": 1020, "distance": 10214}
        ] 
      },
      "8":{
        "distance": 1276,
        "width": 550,
        "comped_dist_min": 225,
        "comped_dist_max": 285,
        "board_start_angle":234,
        "board_end_angle":266.5,
        "refl_board_vec":[
          {"refl":255, "width": 110, "distance": 1308},
          {"refl":90, "width": 220, "distance": 1276, "pos_factor": 0.5},
          {"refl":40, "width": 110, "distance": 1285},
          {"refl":10, "width": 110, "distance": 1315}
        ]
      }
    },
    "refl_board_id_vec": [2, 3, 6, 7, 8],
    "abs_board_id_vec": [4, 8, 3, 1, 7, 2],
    "board_0_2m_id": 6,
    "board_1_2m_id": 8,
    "board_3m_id": 3,
    "board_10m_id": 7,
    "board_20m_id": 2,
    "static_board_id": 3,
    "dynamic_board_id": 1
  },
  "zero":{
    "dist_min": 1000,
    "dist_max": 2500,
    "high_refl_min": 150
  },
  "normal":{
    "dynamic_area_min": 200,
    "dynamic_area_max": 30000,
    "dynamic_remove_low_refl_factor": 0.02,
    "block_3m_refl_delta": [7000,7000,7000,7000,7000,7000,7000,7000,7000,7000,7000,7000],
    "FRONT": {
      "comment": "正装",
      "block_3m_refl_delta": [10000,10000,10000,10000,8000,8000,8000,8000,8000,7000,7000,7000]
    },
    "SIDE": {
      "comment": "侧装",
      "block_3m_refl_delta": [7000,7000,7000,8000,8000,8000,8000,8000,10000,10000,10000,10000]
    },
    "MAPPING": {
      "comment": "测绘",
      "dynamic_area_max": 20000
    }
  }
}