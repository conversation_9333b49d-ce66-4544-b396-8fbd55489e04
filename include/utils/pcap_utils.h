﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef PCAP_PARSE_H
#define PCAP_PARSE_H

#include "rs_logger.h"
#include <memory>
#include <net/ethernet.h>  // 提供以太网头部结构
#include <netinet/ether.h>
#include <netinet/ip.h>   // 提供ip头部结构
#include <netinet/udp.h>  // 提供udp头部结构
#include <pcap.h>

class PcapUtils
{

public:
  PcapUtils() : pcap_handle_(nullptr, pcap_close) {};
  PcapUtils(const PcapUtils&) = delete;
  PcapUtils& operator=(const PcapUtils&) = delete;
  PcapUtils(PcapUtils&&)                 = default;
  PcapUtils& operator=(PcapUtils&&) = default;
  explicit PcapUtils(std::unique_ptr<pcap_t, decltype(&pcap_close)>& _pcap_handle) :
    pcap_handle_(std::move(_pcap_handle))
  {}
  ~PcapUtils() = default;

  struct UdpPacketInfo
  {
    const ip* ip_header      = nullptr;
    const udphdr* udp_header = nullptr;
    const uint8_t* udp_data  = nullptr;
  };

  struct PacketFilter
  {
    std::string device_name;
    std::string ip_src;
    std::string ip_dst;
    int port_src    = -1;
    int port_dst    = -1;
    int data_length = -1;
  };

public:
  bool loadOfflinePcapFile(const std::string& _file_path);
  const uint8_t* getNextOfflinePacket();
  UdpPacketInfo getNextOfflineUdpPacket(const PacketFilter& _filter = { "", "", "", -1, -1, -1 });

private:
  std::unique_ptr<pcap_t, decltype(&pcap_close)> pcap_handle_;
  int data_link_type_ = -1;
  pcap_pkthdr pkthdr_ {};
};

#endif  // PCAP_PARSE_H