﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef LIDAR_CTL_H
#define LIDAR_CTL_H

#include "fsm/finite_state_listener.h"
#include "fsm/finite_state_machine.h"
#include "model/airy_wave_signal_model.h"
#include "model/data_struct.h"

/**
 *  @file  状态机的状态处理
 *  @brief  
 */
namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class AiryWorkHandler : public WorkHandler<AiryWaveSignalModel>
{
  IMPL_CLASSNAME(AgingWorkHandler)
public:
  explicit AiryWorkHandler(const ActionState& _state) : WorkHandler<AiryWaveSignalModel>(_state) {}
  AiryWorkHandler(const AiryWorkHandler&) = delete;
  AiryWorkHandler(AiryWorkHandler&&)      = delete;
  AiryWorkHandler& operator=(const AiryWorkHandler&) = delete;
  AiryWorkHandler& operator=(AiryWorkHandler&&) = delete;
  ~AiryWorkHandler() override                   = default;

  int handleState() override = 0;
  virtual void handleEnter() {}
  virtual void handleExit() {}

  [[nodiscard]] ActionState getNextState() const { return next_state_; }

private:
  ActionState next_state_ = STATE_END;
  std::mutex mutex_;
  std::condition_variable condition_;
};

class CheckAllState : public AiryWorkHandler
{
  IMPL_CLASSNAME(CheckAllState)
public:
  CheckAllState() : AiryWorkHandler(STATE_CHECK_ALL_STATE) {}
  int handleState() override;
};

class ConnectLidar : public AiryWorkHandler
{
  IMPL_CLASSNAME(ConnectLidar)
public:
  ConnectLidar() : AiryWorkHandler(STATE_CONNECT_LIDAR) {}
  int handleState() override;
};

class ZeroRotate : public AiryWorkHandler
{
  IMPL_CLASSNAME(ZeroRotate)
public:
  ZeroRotate() : AiryWorkHandler(STATE_ZERO_ROTATE) {}
  int handleState() override;
};

class ZeroCollect : public AiryWorkHandler
{
  IMPL_CLASSNAME(ZeroCollect)
public:
  ZeroCollect() : AiryWorkHandler(STATE_ZERO_COLLECT) {}
  int handleState() override;
};

class ZeroCalib : public AiryWorkHandler
{
  IMPL_CLASSNAME(ZeroCalib)
public:
  ZeroCalib() : AiryWorkHandler(STATE_ZERO_CALIB) {}
  int handleState() override;
};

class ZeroTest : public AiryWorkHandler
{
  IMPL_CLASSNAME(ZeroTest)
public:
  ZeroTest() : AiryWorkHandler(STATE_ZERO_TEST) {}
  int handleState() override;
};

class InitLidarWave : public AiryWorkHandler
{
  IMPL_CLASSNAME(InitLidarWave)
public:
  InitLidarWave() : AiryWorkHandler(STATE_INIT_LIDAR_WAVE) {}
  int handleState() override;
};

class ResetMotor : public AiryWorkHandler
{
  IMPL_CLASSNAME(ResetMotor)
public:
  ResetMotor() : AiryWorkHandler(STATE_RESET_MOTOR) {}
  int handleState() override;
};

class CollectData : public AiryWorkHandler
{
  IMPL_CLASSNAME(CollectData)
public:
  CollectData() : AiryWorkHandler(STATE_COLLECT_DATA) {}
  int handleState() override;
};

class SaveData : public AiryWorkHandler
{
  IMPL_CLASSNAME(SaveData)
public:
  SaveData() : AiryWorkHandler(STATE_SAVE_DATA) {}
  int handleState() override;
};

class FailState : public AiryWorkHandler
{
  IMPL_CLASSNAME(FailState)
public:
  FailState() : AiryWorkHandler(STATE_FAIL) {}
  int handleState() override;
};

class AbortState : public AiryWorkHandler
{
  IMPL_CLASSNAME(AbortState)
public:
  AbortState() : AiryWorkHandler(STATE_ABORT) {}
  int handleState() override;
};

class SuccessState : public AiryWorkHandler
{
  IMPL_CLASSNAME(SuccessState)
public:
  SuccessState() : AiryWorkHandler(STATE_SUCCESS) {}
  int handleState() override;
};

class FinalState : public AiryWorkHandler
{
  IMPL_CLASSNAME(FinalState)
public:
  FinalState() : AiryWorkHandler(STATE_FINAL) {}
  int handleState() override;
};

}  // namespace lidar
}  // namespace robosense
#endif
