﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef APP_CONTROLLER_H
#define APP_CONTROLLER_H

#include "../ui/mainwindow.h"
#include "fsm/finite_state_machine.h"
#include "model/airy_wave_signal_model.h"
#include <QObject>
#include <memory>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class AppController : public QObject
{
  Q_OBJECT
public:
  explicit AppController(MainWindow* _main_window, QObject* _parent = nullptr);
  AppController(const AppController&) = delete;
  AppController(AppController&&)      = delete;
  AppController& operator=(const AppController&) = delete;
  AppController& operator=(AppController&&) = delete;
  ~AppController() override;

  std::shared_ptr<AiryWaveSignalModel> getWorkModel() { return model_map_[1]; }

  void initWorkModel();
  void updateModelParaInfo();
  void initFsm();
  void startUpFsm();
  void shutDownFsm();
  void initExtDevice();

  void initSignalSlots();

  static float bcdToFloat(const uint16_t _bcd_val);

public Q_SLOTS:
  void slotButtonOneKeyRunClicked();
  void slotButtonTestClicked();
  void slotButtonStopMotorClicked();
  void slotButtonWriteCsvInitClicked();
  void slotButtonUpdateParaInfoClicked();
  void slotButtonReloadConfigParaClicked();
  void slotButtonAutoGetEthClicked();
  void slotButtonStopMotorToAngleClicked();
  void slotButtonStartMotorClicked();
  void slotButtonRwRegFromFileClicked();
  void slotButtonZeroSelfTestClicked();
  void slotButtonZeroCalibClicked();
  void slotButtonZeroSetClicked();
  void slotButtonWriteTopFlashClicked();
  void slotButtonCustomProcessClicked();
  void slotButtonInitLidarClicked();
  void slotButtonWriteChnAngle();
  void slotButtonOneKeyCollectClicked();
  void slotButtonOneKeyProcessClicked();
  void slotButtonWriteBitToLidarClicked();
  void slotButtonAutoSetSnClicked();
  void slotButtonEyeSafeClicked();
  void slotButtonRotateSetupLidarClicked();
  void slotButtonRotateLightSpotClicked();
  void slotButtonDisplayProcessDataClicked();
  void slotButtonCompareClicked();

  void slotButtonConnectClicked(const quint32 _index);
  void slotButtonDisconnectClicked(const quint32 _index);
  void slotButtonOpenDataFolderClicked(const quint32 _index);

  void slotButtonProcessClicked();

  bool slotButtonLoadVbdClicked();
  bool slotButtonVbdLoadAndWriteClicked();
  bool slotButtonVbdWriteFlashClicked();
  bool slotButtonVbdReadClicked();
  bool slotButtonVbdCalculateInterceptClicked();
  bool slotButtonVbdReadCurveClicked();

  void slotVbdLoadFile();

  // void slotCheckBoxChnCtrl(const int _chn, const bool _is_checked);

private:
  MainWindow* main_window_ = nullptr;
  Ui::MainWindow* ui_;

  std::shared_ptr<FiniteStateMachine<AiryWaveSignalModel>> fsm_;
  std::map<uint32_t, std::shared_ptr<AiryWaveSignalModel>> model_map_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // APP_CONTROLLER_H