﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef WORK_MODEL_H
#define WORK_MODEL_H
#include "lidar_manager.h"
#include "utils/decl_name.h"
#include "widgets/project_lidar_info.h"

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class WorkModel
{
public:
  DECL_CLASSNAME(WorkModel);
  explicit WorkModel() = default;
  explicit WorkModel(WidgetLidarInfo* _lidar_info) :
    ptr_lidar_manager_(std::make_shared<LidarManager>(_lidar_info)), lidar_index_(_lidar_info->getLidarIndex())
  {}
  WorkModel(const WorkModel& _other) = default;
  WorkModel& operator=(const WorkModel& _other) = default;
  WorkModel(WorkModel&& _other) noexcept        = default;
  WorkModel& operator=(WorkModel&& _other) noexcept = default;

public:
  virtual ~WorkModel() noexcept = default;

  // virtual bool init()                                                                       = 0;

  std::shared_ptr<LidarManager> getLidarManager() { return ptr_lidar_manager_; }
  void setLidarManager(std::shared_ptr<LidarManager> _lidar_manager) { ptr_lidar_manager_ = std::move(_lidar_manager); }

  [[nodiscard]] int getLidarIndex() const { return lidar_index_; }
  [[nodiscard]] int getLogIndex() const { return lidar_index_; }

  virtual void abort()
  {
    is_abort_ = true;
    getLidarManager()->abort();
  }
  virtual void resetAbort() { is_abort_ = false; };

private:
  std::shared_ptr<LidarManager> ptr_lidar_manager_;
  bool is_abort_   = false;
  int lidar_index_ = -1;
};

}  // namespace lidar
}  // namespace robosense
#endif  // WORK_MODEL_H
