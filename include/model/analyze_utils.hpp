﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef ANALYZE_UTILS_H
#define ANALYZE_UTILS_H
#include "data_struct.h"
#include "rs_logger.h"
#include <algorithm>
#include <cmath>
#include <cstdlib>
#include <numeric>
#include <optional>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class AnalyzeUtils
{
public:
  explicit AnalyzeUtils();
  AnalyzeUtils(AnalyzeUtils&&)      = default;
  AnalyzeUtils(const AnalyzeUtils&) = default;
  AnalyzeUtils& operator=(AnalyzeUtils&&) = default;
  AnalyzeUtils& operator=(const AnalyzeUtils&) = default;
  ~AnalyzeUtils();

  template <typename T>
  static void sortVectors(std::vector<T>& _vec_x, std::vector<T>& _vec_y)
  {
    // 将 _vec_x 和 _vec_y 组合成一对对的形式
    std::vector<std::pair<T, T>> combined(_vec_x.size());
    for (size_t i = 0; i < _vec_x.size(); ++i)
    {
      combined[i] = std::make_pair(_vec_x[i], _vec_y[i]);
    }

    // 根据 _vec_x 的值进行排序
    std::sort(combined.begin(), combined.end(),
              [](const std::pair<T, T>& _lhs, const std::pair<T, T>& _rhs) { return _lhs.first < _rhs.first; });

    // 排序后的结果直接解构回 _vec_x 和 _vec_y
    for (size_t i = 0; i < combined.size(); ++i)
    {
      _vec_x[i] = combined[i].first;
      _vec_y[i] = combined[i].second;
    }
  }

  template <typename T>
  static T getMidValue(const std::vector<T>& _data, const size_t _index, const size_t _window_size)
  {
    if (_data.size() < _window_size)
    {
      LOG_ERROR("Data size is too small. data size: {}, window size: {}", _data.size(), _window_size);
      return 0;
    }

    if (_index < _window_size / 2 || _index > _data.size() - _window_size / 2)
    {
      LOG_ERROR("Index out of range. index: {}, window size: {}", _index, _window_size);
      return 0;
    }

    std::vector<T> temp_data(_window_size);
    for (size_t i = 0; i < _window_size; ++i)
    {
      temp_data[i] = _data[_index - _window_size / 2 + i];
    }

    std::sort(temp_data.begin(), temp_data.end());
    return temp_data[_window_size / 2];
  }

  template <typename T>
  static size_t getMidValueIndex(const std::vector<T>& _data, const size_t _index, const size_t _window_size)
  {

    if (_data.size() < _window_size)
    {
      LOG_ERROR("Data size is too small. data size: {}, window size: {}", _data.size(), _window_size);
      return 0;
    }

    if (_index < _window_size / 2 || _index > _data.size() - _window_size / 2)
    {
      LOG_ERROR("Index out of range. index: {}, window size: {}", _index, _window_size);
      return 0;
    }

    // 窗口范围的起始和结束位置
    size_t start = _index - _window_size / 2;
    size_t end   = start + _window_size;

    // 复制窗口范围的数据并排序
    std::vector<T> window_data(_data.begin() + start, _data.begin() + end);
    std::sort(window_data.begin(), window_data.end());

    // 找到中值
    T mid_value = window_data[_window_size / 2];

    // 返回中值在原数据中的索引
    for (size_t i = start; i < end; ++i)
    {
      if (_data[i] == mid_value)
      {
        return i;
      }
    }

    return _index;
  }

  template <typename T1, typename T2>
  static size_t getMidValueIndexWithSameCodeMark(const std::vector<T1>& _data,
                                                 const std::vector<T2>& _code_mark,
                                                 const size_t _index,
                                                 const size_t _window_size)
  {
    if (_data.size() < _window_size || _code_mark.size() < _window_size)
    {
      LOG_ERROR("Data size is too small. data size: {}, code mark size: {}, window size: {}", _data.size(),
                _code_mark.size(), _window_size);
      return _index;
    }

    if (_index < _window_size / 2 || _index > _data.size() - _window_size / 2)
    {
      LOG_ERROR("Index out of range. index: {}, window size: {}", _index, _window_size);
      return _index;
    }

    // 窗口范围的起始和结束位置
    size_t start = _index - _window_size / 2;
    size_t end   = start + _window_size;

    // 复制窗口范围的数据并排序
    std::vector<T1> window_data1;
    std::vector<T1> window_data2;

    for (size_t i = start; i < end; ++i)
    {
      if (_code_mark.at(i) == 0)
      {
        window_data1.emplace_back(_data[i]);
      }
      else
      {
        window_data2.emplace_back(_data[i]);
      }
    }

    std::sort(window_data1.begin(), window_data1.end());
    std::sort(window_data2.begin(), window_data2.end());

    if (window_data1.empty() && window_data2.empty())
    {
      return _index;
    }

    // 计算两个窗口的斜率和方差
    double slope1 = 0.0;
    double slope2 = 0.0;
    // double var1   = 0.0;
    // double var2   = 0.0;

    for (size_t i = 1; i < window_data1.size(); ++i)
    {
      slope1 += window_data1[i] - window_data1[i - 1];
      // var1 += window_data1[i] * window_data1[i];
    }
    for (size_t i = 1; i < window_data2.size(); ++i)
    {
      slope2 += window_data2[i] - window_data2[i - 1];
      // var2 += window_data2[i] * window_data2[i];
    }

    if (!window_data1.empty())
    {
      slope1 /= window_data1.size();
    }
    if (!window_data2.empty())
    {
      slope2 /= window_data2.size();
    }

    // var1 /= window_data1.size();
    // var2 /= window_data2.size();

    // LOG_INFO("slope1: {}, slope2: {}", slope1, slope2);
    // LOG_INFO("var1: {}, var2: {}", var1, var2);

    // 输出方差和斜率，选择斜率绝对值较小的窗口，寻找该窗口中值的索引
    int target_value = 0;

    if (window_data1.empty())
    {
      target_value = window_data2[window_data2.size() / 2];
    }
    else if (window_data2.empty())
    {
      target_value = window_data1[window_data1.size() / 2];
    }
    else
    {
      target_value = window_data1[window_data1.size() / 2];
      if (std::abs(slope1) > std::abs(slope2))
      {
        target_value = window_data2[window_data2.size() / 2];
      }
    }

    // 从中间_index开始，向前后两边查找中值，返回最靠近_index的中值的索引
    auto left_index  = _index - 1;
    auto right_index = _index + 1;
    while (left_index >= start && right_index < end)
    {
      if (_data[left_index] == target_value)
      {
        return left_index;
      }
      if (_data[right_index] == target_value)
      {
        return right_index;
      }
      --left_index;
      ++right_index;
    }

    return _index;
  }

  template <typename T>
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  static T getMeanValue(const std::vector<T>& _data, const size_t _index, const size_t _window_size)
  {
    if (_data.size() < _window_size)
    {
      LOG_ERROR("Data size is too small. data size: {}, window size: {}", _data.size(), _window_size);
      return 0;
    }
    if (_index < _window_size / 2 || _index > _data.size() - _window_size / 2)
    {
      LOG_ERROR("Index out of range. index: {}, window size: {}", _index, _window_size);
      return 0;
    }

    std::vector<T> data;
    for (size_t i = _index - (_window_size / 2); i < _index + (_window_size / 2); ++i)
    {
      // 如果这个点是异常值，则不参与计算，此点与前与后都差距500以上
      if (i > 0 && i < _data.size() - 1 && std::abs(_data[i] - _data[i - 1]) > 300 &&
          std::abs(_data[i] - _data[i + 1]) > 300)
      {
        continue;
      }
      if (_data[i] < 1)
      {
        continue;
      }
      data.emplace_back(_data[i]);
    }

    T sum = 0;
    for (const auto& value : data)
    {
      sum += value;
    }

    if (data.empty())
    {
      return 0;
    }

    return sum / data.size();
  }

  template <typename T>
  static T getMeanValue(const std::vector<T>& _data)
  {
    if (_data.empty())
    {
      return 0;
    }
    T sum = 0;
    return getMeanValue(_data, _data.size() / 2, _data.size() / 2);
  }

  // 获取统计学参数
  template <typename T>
  struct StatisticParam
  {
    T mean;
    T range;
    T standard_deviation;
    T error_standard_deviation;
    T min_val;
    T max_val;
  };

  template <typename T>
  static std::optional<StatisticParam<T>> getStatisticParam(const std::vector<T>& _data, const T& _true_value)
  {
    StatisticParam<T> param;
    if (_data.empty())
    {
      LOG_ERROR("Data size is too small. data size: {}", _data.size());
      return {};
    }

    // 计算均值 (mean)
    T sum      = std::accumulate(_data.begin(), _data.end(), T(0));
    param.mean = sum / static_cast<T>(_data.size());

    // 计算极差 (range)
    auto min_max  = std::minmax_element(_data.begin(), _data.end());
    param.range   = *min_max.second - *min_max.first;
    param.min_val = *min_max.first;
    param.max_val = *min_max.second;

    // 计算标准差 (standard deviation)
    T variance = 0;
    for (const auto& value : _data)
    {
      variance += std::pow(value - param.mean, 2);
    }
    variance /= static_cast<T>(_data.size());
    param.standard_deviation = std::sqrt(variance);

    // 计算误差标准差 (error standard deviation)
    T error_variance = 0;
    for (const auto& value : _data)
    {
      T error = value - _true_value;  // 计算误差
      error_variance += std::pow(error, 2);
    }
    error_variance /= static_cast<T>(_data.size());
    param.error_standard_deviation = std::sqrt(error_variance);

    return param;
  }

  static bool findBoardRangeByWindow(DistAreaCodeMark& _dist_vec_area_map,
                                     int _curr_index,
                                     const size_t _window_size,
                                     BoardInfo& _board_info,
                                     bool _is_use_raw_dist = true)
  {
    auto& dist_vec = _dist_vec_area_map.dist_comped_vec;
    if (dist_vec.empty())
    {
      LOG_ERROR("dist_vec is empty");
      return false;
    }
    if (_curr_index >= static_cast<int>(dist_vec.size()))
    {
      _curr_index = static_cast<int>(dist_vec.size() - _window_size);
    }
    _curr_index   = std::max(_curr_index, 0);
    auto dist_min = _is_use_raw_dist ? _board_info.raw_dist_min : _board_info.comped_dist_min;
    auto dist_max = _is_use_raw_dist ? _board_info.raw_dist_max : _board_info.comped_dist_max;

    if (dist_min < 0 || dist_max < 0)
    {
      LOG_ERROR("读取靶板筛靶最大最小值失败, dist_min: {}, dist_max: {}", dist_min, dist_max);
      return false;
    }

    if (_board_info.board_index_min > 0 && _curr_index < _board_info.board_index_min)
    {
      _curr_index = _board_info.board_index_min;
    }

    size_t start_index = _curr_index;
    for (; start_index < (dist_vec.size() - _board_info.board_index_length); start_index += _window_size)
    {
      // 通过滑动窗口找到起始点
      if (!findBoardRangeStartByWindow(_dist_vec_area_map, _window_size, _board_info, start_index, _is_use_raw_dist))
      {
        return false;
      }

      // 通过滑动窗口找到结束点
      size_t end_index = start_index + _window_size;
      if (!findBoardRangeEndByWindow(_dist_vec_area_map, _window_size, _board_info, end_index, _is_use_raw_dist))
      {
        return false;
      }

      // 判断区间长度是否匹配
      if (end_index - start_index < _board_info.board_index_length)
      {
        start_index = end_index;
        // LOG_DEBUG("Board {} 匹配失败, index length 不匹配, start_index: {}, end_index: {}, index_length: {}",
        // _board_info.board_id, start_index, end_index, end_index - start_index);
        continue;
      }

      // if (!fixStartByWindow(_data, _window_size, _board_info, start_index))
      // {
      //   return false;
      // }

      // if (!fixEndByWindow(_data, _window_size, _board_info, end_index))
      // {
      //   return false;
      // }

      _board_info.detected_data_start = static_cast<int>(start_index);
      _board_info.detected_data_end   = static_cast<int>(end_index);
      _board_info.is_found            = true;

      // LOG_DEBUG("Board {} 匹配成功, start_index: {}, end_index: {}, index_length: {}", _board_info.board_id, start_index,
      //           end_index, end_index - start_index);
      return true;
    }

    LOG_ERROR("Board {} 匹配失败, 找不到匹配的靶板", _board_info.board_id);
    return false;
  }

  static void eraseOutlier(std::vector<float>& _vec1,
                           std::vector<float>& _vec2,
                           const int _k_neighbor,
                           const float _threshold)
  {
    if (_vec1.size() != _vec2.size() || _vec1.empty() || _vec1.size() < _k_neighbor)
    {
      // std::cerr << "Invalid parameters in eraseOutlier!" << std::endl;
      LOG_ERROR("vec1 size: {}, vec2 size: {}, k_neighbor: {}", _vec1.size(), _vec2.size(), _k_neighbor);
      return;
    }

    auto vec1 = _vec1;
    auto vec2 = _vec2;

    minMaxNormalization(vec1);
    minMaxNormalization(vec2);

    // 遍历每个点，计算其K近邻平均距离，与threshold比较
    std::map<int, bool> outlier_indices_map;

    if (vec1.size() > 20)
    {
      outlier_indices_map[0]                                 = true;
      outlier_indices_map[1]                                 = true;
      outlier_indices_map[static_cast<int>(vec1.size()) - 1] = true;
      outlier_indices_map[static_cast<int>(vec1.size()) - 2] = true;
    }

    std::vector<float> avg_dist_vec;
    for (int i = 0; i < vec1.size(); ++i)
    {
      size_t start = std::max(0, i - _k_neighbor);
      size_t end   = std::min(static_cast<int>(vec1.size()), i + _k_neighbor);

      std::vector<float> distances;
      for (size_t j = start; j < end; ++j)
      {
        if (j == i)
        {
          continue;
        }
        distances.emplace_back(std::sqrt(std::pow(vec1[i] - vec1[j], 2) + std::pow(vec2[i] - vec2[j], 2)));
      }
      float avg = std::accumulate(distances.begin(), distances.end(), 0.0) / static_cast<float>(distances.size());
      avg_dist_vec.emplace_back(avg);
      if (avg > _threshold)
      {
        outlier_indices_map[i] = true;
      }
    }
    std::vector<float> vec1_filtered;
    std::vector<float> vec2_filtered;

    for (int i = 0; i < static_cast<int>(_vec1.size()); ++i)
    {
      if (outlier_indices_map.find(i) != outlier_indices_map.end())
      {
        continue;
      }
      vec1_filtered.emplace_back(_vec1[i]);
      vec2_filtered.emplace_back(_vec2[i]);
    }
    _vec1 = vec1_filtered;
    _vec2 = vec2_filtered;
  }

private:
  static bool findBoardRangeStartByWindow(DistAreaCodeMark& _dist_vec_area_map,
                                          const size_t _window_size,
                                          const BoardInfo& _board_info,
                                          size_t& _start_index,
                                          bool _is_use_raw_dist)
  {
    const auto& dist_vec = _dist_vec_area_map.dist_comped_vec;
    const auto& area_vec = _dist_vec_area_map.area_vec;
    auto dist_min        = _is_use_raw_dist ? _board_info.raw_dist_min : _board_info.comped_dist_min;
    auto dist_max        = _is_use_raw_dist ? _board_info.raw_dist_max : _board_info.comped_dist_max;
    // 窗口滑动
    for (size_t i = _start_index; i < dist_vec.size() - _board_info.board_index_length; i++)
    {
      // 判断窗口内的数据是否符合靶板的特征
      float match_rate = 0.99;
      if (dist_vec[i] > dist_max || dist_vec[i] < dist_min)
      {
        continue;
      }
      if (_board_info.board_start_area_min > 0 && area_vec.at(i) < _board_info.board_start_area_min)
      {
        continue;
      }
      if (_board_info.board_start_area_min > 0 && area_vec.at(i + 1) < _board_info.board_start_area_min)
      {
        continue;
      }
      if (_board_info.board_start_area_min > 0 && area_vec.at(i + 2) < _board_info.board_start_area_min)
      {
        continue;
      }
      if (checkIfWindowMatchBoardRange(_dist_vec_area_map, i, _window_size, true, match_rate, _board_info,
                                       _is_use_raw_dist))
      {
        _start_index = i;
        return true;
      }
    }

    // 找不到符合的窗口
    LOG_ERROR("找不到符合的start窗口, board id: {}, window size: {}, dist_min: {} dist_max: {}", _board_info.board_id,
              _window_size, dist_min, dist_max);
    return false;
  }
  static bool findBoardRangeEndByWindow(DistAreaCodeMark& _dist_vec_area_map,
                                        const size_t _window_size,
                                        const BoardInfo& _board_info,
                                        size_t& _end_index,
                                        bool _is_use_raw_dist)
  {
    auto& dist_vec = _dist_vec_area_map.dist_comped_vec;
    // 窗口滑动
    size_t window_size = std::lround(static_cast<float>(_window_size) * 0.8);

    float match_rate = 0.5;
    size_t index     = _end_index;

    for (; index < dist_vec.size() - window_size; index++)
    {
      // 找到最后一个符合的窗口
      if (checkIfWindowMatchBoardRange(_dist_vec_area_map, index, _window_size, false, match_rate, _board_info,
                                       _is_use_raw_dist))
      {
        _end_index = index + _window_size;
        continue;
      }
      match_rate = 0.5;
      // 随后的连续两个窗口也符合的时候，则是出现了零散的点，需要继续找结束点
      if (checkIfWindowMatchBoardRange(_dist_vec_area_map, index + _window_size, _window_size, false, match_rate,
                                       _board_info, _is_use_raw_dist))
      {
        index += _window_size;
        _end_index = index + _window_size;
        continue;
      }

      // match_rate = 0.4;
      // if (checkIfWindowMatchBoardRange(_data, i + 2 * _window_size, _window_size, match_rate, _board_info))
      // {
      //   i += _window_size + _window_size;
      //   _end_index = i + _window_size;
      //   continue;
      // }

      // if (checkIfWindowMatchBoardRange(_data, i + 3 * _window_size, _window_size, 0.4, _board_info))
      // {
      //   i += _window_size + _window_size + _window_size;
      //   _end_index = i + _window_size;
      //   continue;
      // }

      // 当不符合的时候，直接返回，_end_index已经记录了上一个符合的窗口
      return true;
    }
    if (index >= dist_vec.size() - window_size)
    {
      float match_rate = 0.5;
      if (checkIfWindowMatchBoardRange(_dist_vec_area_map, index - 1, window_size, false, match_rate, _board_info,
                                       _is_use_raw_dist))
      {
        _end_index = dist_vec.size() - 1;
        return true;
      }
    }

    // 找不到符合的窗口
    LOG_ERROR("找不到符合的end窗口, board id: {}, window size: {}", _board_info.board_id, _window_size);
    return false;
  }

  // template <typename T>
  // static bool fixEndByWindow(const std::vector<T>& _dist_vec,
  //                            const std::vector<T>& _area,
  //                            const std::vector<T>& _code_mark,
  //                            const size_t _window_size,
  //                            const BoardInfo& _board_info,
  //                            size_t& _end_index)
  // {
  //   // 通过滑动串口，从起始end_index 位置开始往前找，找到符合的窗口,match rate大于0.95的
  //   for (size_t i = _end_index; i > (_end_index - _board_info.board_index_length - _window_size); i--)
  //   {
  //     // 判断窗口内的数据是否符合靶板的特征
  //     float match_rate = 0.97;
  //     if (checkIfWindowMatchBoardRange(_dist_vec, i - _window_size, _window_size, match_rate, _board_info))
  //     {
  //       if (_dist_vec.at(i) < _board_info.raw_dist_min || _dist_vec.at(i) > _board_info.raw_dist_max)
  //       {
  //         continue;
  //       }
  //       if (checkIfWindowMatchBoardRange(_dist_vec, i - 2 * _window_size, _window_size, match_rate, _board_info))
  //       {
  //         _end_index = i;
  //         return true;
  //       }
  //     }
  //   }
  //   LOG_ERROR("修正串口end失败, board id: {}, window size: {}", _board_info.board_id, _window_size);
  //   return false;
  // }

  static bool checkIfWindowMatchBoardRange(DistAreaCodeMark& _dist_vec_area_map,
                                           const size_t _start_index,
                                           const size_t _window_size,
                                           const bool _is_start_point,
                                           float& _match_rate,
                                           const BoardInfo& _board_info,
                                           bool _is_use_raw_dist)
  {
    auto& dist_vec = _dist_vec_area_map.dist_comped_vec;
    if (_window_size == 0)
    {
      LOG_ERROR("Window size is 0");
      return false;
    }
    auto end_index = _start_index + _window_size;

    auto dist_min = _is_use_raw_dist ? _board_info.raw_dist_min : _board_info.comped_dist_min;
    auto dist_max = _is_use_raw_dist ? _board_info.raw_dist_max : _board_info.comped_dist_max;

    if (_board_info.board_index_min > 0 && _start_index < _board_info.board_index_min)
    {
      LOG_DEBUG("Board index min 不匹配, _start_index: {}, _end_index: {}, board_index_min: {}", _start_index,
                end_index, dist_min);
      return false;
    }
    if (_board_info.board_index_max > 0 && end_index > _board_info.board_index_max)
    {
      LOG_DEBUG("Board index max 不匹配, _start_index: {}, _end_index: {}, board_index_max: {}", _start_index,
                end_index, dist_max);
      return false;
    }

    float un_match_count = 0;
    for (size_t i = _start_index; i < end_index; ++i)
    {
      if (dist_vec[i] < _board_info.ignored_dist)
      {
        continue;
      }
      if (dist_min > 0 && dist_vec[i] < dist_min)
      {
        ++un_match_count;
        continue;
      }
      if (dist_max > 0 && dist_vec[i] > dist_max)
      {
        ++un_match_count;
        continue;
      }
    }
    float real_match_rate = 1 - (un_match_count / static_cast<float>(end_index - _start_index));

    if (real_match_rate < _match_rate)
    {
      // LOG_DEBUG("Board {} 匹配失败, match rate 不匹配, _start_index: {}, _end_index: {}, board_index_min: {}, "
      //           "board_index_max: {}, board_dist_vec_min: {}, board_dist_vec_max: {}, match rate: {}",
      //           _board_info.board_id, _start_index, _end_index, _board_info.board_index_min, _board_info.board_index_max,
      //           _board_info.raw_dist_min, _board_info.raw_dist_max, real_match_rate);
      return false;
    }

    return true;
  }

  static void minMaxNormalization(std::vector<float>& _data)
  {
    if (_data.empty())
    {
      return;
    }

    // 找到数据的最小值和最大值
    float min_val = *std::min_element(_data.begin(), _data.end());
    float max_val = *std::max_element(_data.begin(), _data.end());

    // 归一化
    for (auto& val : _data)
    {
      val = (val - min_val) / (max_val - min_val);
    }
  }
};

}  // namespace lidar
}  // namespace robosense
#endif  // ANALYZE_UTILS_H