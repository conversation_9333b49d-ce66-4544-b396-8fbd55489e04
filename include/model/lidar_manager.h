﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef LIDAR_MANAGER_H
#define LIDAR_MANAGER_H

#include "mech_communication/protocol/data_struct/mech.h"
#include "widgets/project_lidar_info.h"
#include <cstdint>
#include <memory>

class QString;

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

namespace mech
{
struct ConfigPara;
}  // namespace mech

class MechLidarManager;
class MechCommunication;
class CsvUtils;

class LidarManager
{
public:
  explicit LidarManager(WidgetLidarInfo* _lidar_info);
  LidarManager(const LidarManager&) = default;
  LidarManager& operator=(const LidarManager&) = default;
  LidarManager(LidarManager&&) noexcept        = default;
  LidarManager& operator=(LidarManager&&) noexcept = default;
  ~LidarManager()                                  = default;

  enum class MotorSpeedType : uint8_t
  {
    MOTOR_SPEED_0 = 0,
    MOTOR_SPEED_300,
    MOTOR_SPEED_600,
    MOTOR_SPEED_900,
    MOTOR_SPEED_1200,
  };

  struct Version
  {
    uint32_t pl_version;
    uint32_t ps_version;
    uint32_t app_version;
    uint32_t motor_version;
    uint32_t config_version;
  };

  [[nodiscard]] int getLidarIndex() const { return static_cast<int>(lidar_info_->getLidarIndex()); }
  [[nodiscard]] int getLogIndex() const { return static_cast<int>(lidar_info_->getLidarIndex()); }

  void setLidarInfoIP(const QString& _ip);
  void setLidarInfoMSOP(const quint16 _msop);
  void setLidarInfoDIFOP(const quint16 _difop);
  bool scanFirstLidarAndSetIP();
  bool waitForTop();
  bool connect();
  bool disconnect();
  bool setMotorSpeed(const MotorSpeedType _motor_speed);
  bool stopMotor();
  bool setZeroAngle(float _angle);
  bool writeTopFlash(const QString& _file_path, const uint32_t _addr_start, const uint32_t _addr_end);

  std::optional<mech::MountType> getMountType();

  bool writeTopFlash(const QByteArray& _data, const uint32_t _addr_start);

  bool writeReflBit(const QString& _file_path);
  bool writeDynamicBit(const QString& _file_path);
  bool writeStaticBit(const QString& _file_path);
  bool writeTwoDimBit(const QString& _file_path);
  bool writeAbsBit(const QString& _file_path);

  bool writeVbdGdi(const uint32_t _vbd_intercept_hex, const uint32_t _vbd_err_hex);

  bool setEyeSafe(const bool _is_open);

  bool stopMotorToAngle(const double& _angle);
  bool startMotor();

  bool getConfigParam(mech::ConfigPara& _config_para);
  Version getVersion();

  template <typename T>
  bool writeCmd(const uint32_t _cmd_type, const T _value, const uint32_t _msec = 4000);

  bool writeCmd(const uint32_t _cmd_type, const std::vector<uint8_t>& _data, const uint32_t _msec = 4000);

  template <typename T>
  bool readCmd(const uint32_t _cmd_type, T& _value, const uint32_t _msec = 4000);

  bool autoSetLidarSN();

  bool writeChnAngle();
  bool readChnAngle(std::vector<float>& _ver_chn_angle_vec, std::vector<float>& _hor_chn_angle_vec);

  bool writeVbdBias();
  bool writeVbdBias(uint8_t _version);

  bool readReg459Pair(const uint32_t _reg_addr, uint32_t& _reg_val);

  bool readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2);
  bool readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex);

public:
  void loadConfigData();
  void setLidarInfo(WidgetLidarInfo* _lidar_info);
  [[nodiscard]] WidgetLidarInfo* getLidarInfo() const;
  bool ping();
  void abort();
  void abortWritingFlash();
  bool getRegisterAddr(const QString& _register_name, uint32_t& _register_addr);

  bool writeCsvData(const QString& _key);
  bool writeCsvDataAfterCalib(const QString& _key);

  bool writeCsvCmdData(const QString& _key);
  bool writeCsvCmdDataAfterCalib(const QString& _key);

  bool writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec = 10000);
  bool readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = 10000);

  bool readTopRegDataByKey(const QString& _key, uint32_t& _data, const uint32_t _byte_size, const int _timeout = 10000);
  bool writeTopRegDataByKey(const QString& _key,
                            const uint32_t _data,
                            // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                            const uint32_t _byte_size,
                            const int _timeout = 10000);
  bool writeTopRegData(const uint32_t _reg_addr,
                       const uint32_t _reg_val,
                       const uint32_t _byte_size,
                       const uint32_t _msec = 10000);
  bool readTopRegData(const uint32_t _reg_addr,
                      uint32_t& _reg_val,
                      const uint32_t _byte_size,
                      const uint32_t _msec = 10000);
  float getChnHorizontalAngle(const uint32_t _chn_num);

private:
  WidgetLidarInfo* lidar_info_ = nullptr;
  std::shared_ptr<CsvUtils> reg_csv_utils_ptr_;
  std::shared_ptr<CsvUtils> angle_48_csv_utils_ptr_;
  std::shared_ptr<CsvUtils> angle_96_csv_utils_ptr_;
  std::shared_ptr<MechCommunication> mech_communication_ptr_;
  std::vector<std::vector<float>> angle_vec_vec_48_;
  std::vector<std::vector<float>> angle_vec_vec_96_;
  std::vector<float> ver_chn_angle_vec_;
  std::vector<float> hor_chn_angle_vec_;
  mech::ConfigPara config_para_;
  mech::DifopPacket difop_packet_;
  uint32_t config_version_ = 0;
  bool is_abort_           = false;
};

}  // namespace lidar
}  // namespace robosense

#endif  // LIDAR_MANAGER_H