/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "model/b_spline_fit.hpp"
#include "model/poly_fit.hpp"

#include "rs_logger.h"
#include <algorithm>
#include <array>
#include <cstddef>
#include <cstdint>
#include <map>

#if defined(_MSC_VER)  // MSVC
#  include <intrin.h>
#  define BSWAP16 _byteswap_ushort
#  define BSWAP32 _byteswap_ulong
#  define BSWAP64 _byteswap_uint64
#elif defined(__GNUC__) || defined(__clang__)  // GCC or Clang
#  define BSWAP16 __builtin_bswap16
#  define BSWAP32 __builtin_bswap32
#  define BSWAP64 __builtin_bswap64
#else
#  error "Unsupported compiler"
#endif

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

struct PointsInt
{
  std::vector<int> x_vec;
  std::vector<int> y_vec;

  void sortByX()
  {
    // 创建索引数组
    std::vector<size_t> indices(x_vec.size());
    for (size_t i = 0; i < indices.size(); ++i)
    {
      indices[i] = i;
    }

    // 根据x_vec中的值对索引进行排序
    std::sort(indices.begin(), indices.end(),
              [this](const size_t& _lhs, const size_t& _rhs) { return x_vec[_lhs] < x_vec[_rhs]; });

    // 根据排序后的索引重新排列x_vec和y_vec
    std::vector<int> sorted_x(x_vec.size());
    std::vector<int> sorted_y(y_vec.size());
    for (size_t i = 0; i < indices.size(); ++i)
    {
      sorted_x[i] = x_vec[indices[i]];
      sorted_y[i] = y_vec[indices[i]];
    }

    // 将排序后的结果赋值回原向量
    x_vec = std::move(sorted_x);
    y_vec = std::move(sorted_y);
  }
};
struct ReflBoardPos
{
  int refl               = -1;
  float width            = -1;
  float pos_factor       = -1;
  int area_index         = -1;
  int amp_index          = -1;
  float distance_05cm    = -1;
  float distance         = -1;
  float dist_mean        = 0;
  float dist_range       = 0;
  float dist_err_sd      = 0;  // 误差标准差
  float area_mean        = 0;
  float amp_mean         = 0;
  float dist_mean_code_0 = 0;
  float area_mean_code_0 = 0;
  float amp_mean_code_0  = 0;
  float dist_mean_code_1 = 0;
  float area_mean_code_1 = 0;
  float amp_mean_code_1  = 0;
};
using BoardRefl = int;
using BoardId   = int;
struct BoardInfo
{
  int board_id                    = -1;
  int raw_dist_min                = -1;
  int raw_dist_max                = -1;
  int comped_dist_min             = -1;
  int comped_dist_max             = -1;
  int board_index_min             = -1;
  int board_index_max             = -1;
  int ignored_dist                = -1;
  int detected_data_start         = -1;
  int detected_data_end           = -1;
  float distance_05cm             = -1;
  float distance                  = -1;
  float width                     = -1;
  float board_start_angle         = -1;
  float board_end_angle           = -1;
  int board_index_length          = -1;
  int board_start_area_min        = -1;
  int board_end_area_min          = -1;
  float board_start_remove_factor = -1;
  float board_end_remove_factor   = -1;
  bool is_found                   = false;
  std::vector<ReflBoardPos> refl_board_vec;

  ReflBoardPos getReflBoard(const BoardRefl& _refl)
  {
    for (auto& refl_board_pos : refl_board_vec)
    {
      if (refl_board_pos.refl == _refl)
      {
        return refl_board_pos;
      }
    }
    return {};
  }
};
struct DistAreaCodeMark
{
  std::vector<int> dist_vec;
  std::vector<int> area_vec;
  std::vector<int> amp_vec;
  std::vector<int> code_mark_vec;

  std::vector<float> dist_comped_vec;

  std::map<int, BoardInfo> board_info_map;
  std::map<int, BoardInfo> board_info_map_bak;

  // std::vector<float> dynamic_comp_fit_result;
  std::vector<std::vector<float>> dynamic_comp_result;

  std::vector<float> static_comp_result;

  std::map<BoardRefl, std::vector<int>> area_comp_fit_result_map;

  std::map<BoardRefl, std::vector<int>> amp_comp_fit_result_map;

  std::vector<float> abs_coe_k_vec;
  std::vector<float> abs_coe_b_vec;

  std::vector<float> dist_true_vec;
  std::vector<float> dist_test_vec;
  std::vector<float> dist_error_vec;

  int dynamic_start_index;
  int dynamic_board_id;
  int dynamic_code_mark;
  float dynamic_min_dist;

  bool refl90_is_min_area;
  bool is_dynamic_static_comp;

  ReflBoardPos getReflBoard(const int _board_id, const BoardRefl& _refl)
  {
    if (board_info_map.find(_board_id) != board_info_map.end())
    {
      return board_info_map[_board_id].getReflBoard(_refl);
    }
    return {};
  }
};

struct ZeroDataPoint
{
  uint16_t dist;
  uint16_t area;
  uint16_t azi;
};
struct ZeroAziMapData
{
  double dist_sum { 0. };
  double area_sum { 0. };
  double dist_mean { 0. };
  double area_mean { 0. };
  int count { 1 };
};

#pragma pack(push, 1)
struct DistArea
{
  uint16_t dist;
  uint8_t refl;
  void toBigEndian() { dist = BSWAP16(dist); }
};
struct DataBlock96
{
  uint16_t ide1;
  uint16_t azimuth1;
  std::array<DistArea, 48> dist_refl1;
  uint16_t ide2;
  uint16_t azimuth2;
  std::array<DistArea, 48> dist_refl2;
  void toBigEndian()
  {
    ide1     = BSWAP16(ide1);
    azimuth1 = BSWAP16(azimuth1);
    for (auto& dist_refl : dist_refl1)
    {
      dist_refl.toBigEndian();
    }
    ide2     = BSWAP16(ide2);
    azimuth2 = BSWAP16(azimuth2);
    for (auto& dist_refl : dist_refl2)
    {
      dist_refl.toBigEndian();
    }
  }
};

struct DataBlock
{
  uint16_t ide;
  uint16_t azimuth;
  std::array<DistArea, 48> dist_refl;
  void toBigEndian()
  {
    ide     = BSWAP16(ide);
    azimuth = BSWAP16(azimuth);
    for (auto& dist_refl : dist_refl)
    {
      dist_refl.toBigEndian();
    }
  }
};

constexpr uint32_t MSOP_PKT_HEAD = 0x55aa055a;

union MsopPacket
{
  struct
  {
    uint32_t pkt_head;
    uint32_t rev0;
    uint32_t pktcnt_toptobot;
    uint32_t pktcnt_bottops;
    uint16_t data_type;
    uint16_t rev1;
    std::array<uint8_t, 6> timestamp_sec;
    uint32_t timestamp_nano;
    uint8_t rev2;
    uint8_t lidar_type;
    uint8_t lidar_model;
    std::array<uint8_t, 5> rev3;
    uint16_t rx_temp;
    uint16_t topboard_temp;
    std::array<DataBlock, 8> data_block;
    std::array<uint8_t, 6> tails;
    std::array<uint8_t, 16> rev4;
  };
  std::array<char, 1248> arr;

  void toBigEndian()
  {
    pkt_head        = BSWAP32(pkt_head);
    rev0            = BSWAP32(rev0);
    pktcnt_toptobot = BSWAP32(pktcnt_toptobot);
    pktcnt_bottops  = BSWAP32(pktcnt_bottops);
    data_type       = BSWAP16(data_type);
    rev1            = BSWAP16(rev1);
    timestamp_nano  = BSWAP32(timestamp_nano);
    // rx_temp         = BSWAP16(rx_temp);
    topboard_temp = BSWAP16(topboard_temp);

    // std::reverse(timestamp_sec.begin(), timestamp_sec.end());

    for (auto& block : data_block)
    {
      block.toBigEndian();
    }
  }
  void toLittleEndian() { toBigEndian(); }
  [[nodiscard]] bool isValid()
  {
    if (pkt_head == MSOP_PKT_HEAD)
    {
      return true;
    }
    if (BSWAP32(pkt_head) == MSOP_PKT_HEAD)
    {
      toLittleEndian();
      return true;
    }
    return false;
  }
};

union MsopPacket96
{
  struct
  {
    uint32_t pkt_head;
    uint32_t rev0;
    uint32_t pktcnt_toptobot;
    uint32_t pktcnt_bottops;
    uint16_t data_type;
    uint16_t rev1;
    std::array<uint8_t, 6> timestamp_sec;
    uint32_t timestamp_nano;
    uint8_t rev2;
    uint8_t lidar_type;
    uint8_t lidar_model;
    std::array<uint8_t, 5> rev3;
    uint16_t rx_temp;
    uint16_t topboard_temp;
    std::array<DataBlock96, 4> data_block;
    std::array<uint8_t, 6> tails;
    std::array<uint8_t, 16> rev4;
  };
  std::array<char, 1248> arr;

  void toBigEndian()
  {
    pkt_head        = BSWAP32(pkt_head);
    rev0            = BSWAP32(rev0);
    pktcnt_toptobot = BSWAP32(pktcnt_toptobot);
    pktcnt_bottops  = BSWAP32(pktcnt_bottops);
    data_type       = BSWAP16(data_type);
    rev1            = BSWAP16(rev1);
    timestamp_nano  = BSWAP32(timestamp_nano);
    // rx_temp         = BSWAP16(rx_temp);
    topboard_temp = BSWAP16(topboard_temp);

    std::reverse(timestamp_sec.begin(), timestamp_sec.end());

    for (auto& block : data_block)
    {
      block.toBigEndian();
    }
  }
  void toLittleEndian() { toBigEndian(); }
  [[nodiscard]] bool isValid()
  {
    if (pkt_head == MSOP_PKT_HEAD)
    {
      return true;
    }
    if (BSWAP32(pkt_head) == MSOP_PKT_HEAD)
    {
      toLittleEndian();
      return true;
    }
    return false;
  }
};

constexpr std::array<int, 96> getChnArray()
{
  std::array<int, 96> arr = {};
  for (size_t i = 1; i <= 96; ++i)
  {
    arr.at(i - 1) = static_cast<int>(i);
  }
  return arr;
}
struct GdiRegU8
{
  uint8_t reg_flag;
  uint8_t reg_addr_high;
  uint8_t reg_addr_low;
  uint8_t reg_data;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint8_t _data)
  {
    reg_flag      = (_addr >> 16U) & 0xffU;
    reg_addr_high = (_addr >> 8U) & 0xffU;
    reg_addr_low  = _addr & 0xffU;
    reg_data      = _data;
  }
};
struct GdiRegU16
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint16_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 1, _data & 0xffU);
  }
};
struct GdiRegU24
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_mid;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint32_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 16U) & 0xffU);
    reg_mid.setData(_addr + 1, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 2, _data & 0xffU);
  }
};

constexpr uint32_t DYNAMIC_GDI_REG_START = 0x8c2170;
// constexpr uint8_t ABS_GDI_REG_START      = 0x50;
constexpr uint32_t GDI_REG_STATIC_ENABLE  = 0x8c2027;
constexpr uint32_t GDI_REG_TEMPERATURE    = 0x8c2017;
constexpr uint32_t GDI_REG_ABS_DIST_START = 0x8c2150;

union DynamicGdiReg
{
  // 前三个字节为地址，最后低位字节为数据
  struct
  {
    uint16_t reg_addr;
    uint16_t reg_addr_val;
  };

  uint32_t reg_data;
};

constexpr std::array<uint16_t, 512> getDynamicCalibAreaArray()
{
  std::array<uint16_t, 512> arr = { 65535 };
  uint16_t index                = 0;
  // 步进64，从0开始，到32704结束
  for (uint16_t area = 0; area < 32768; area += 64)
  {
    arr.at(index++) = area;
  }
  return arr;
}
constexpr std::array<uint16_t, 512> DYNAMIC_COMP_AREA_ARR = getDynamicCalibAreaArray();
struct DynamicComp
{
  std::array<uint16_t, DYNAMIC_COMP_AREA_ARR.size()> dist_val_arr;

  void toBigEndian()
  {
    for (auto& dist : dist_val_arr)
    {
      dist = BSWAP16(dist);
    }
  }
};
union DynamicBit
{
  struct
  {
    std::array<DynamicComp, 96> comp;
    std::array<DynamicGdiReg, 12> gdi_reg;
  };

  std::array<char, static_cast<std::size_t>(192 * 1024)> arr;
  void toBigEndian()
  {
    for (auto& com : comp)
    {
      com.toBigEndian();
    }
    for (auto& reg : gdi_reg)
    {
      reg.reg_addr_val = BSWAP16(reg.reg_addr_val);
      reg.reg_addr     = BSWAP16(reg.reg_addr);
    }
  }
};

struct StaticComp
{
  uint16_t code4_dist;
  uint16_t code3_dist;
  uint16_t code2_dist;
  uint16_t code1_dist;
};

union StaticBit
{
  struct
  {
    union
    {
      std::array<StaticComp, 96> comp;
      std::array<char, 1024> comp_arr;
    };
    std::array<GdiRegU8, 2> reg;
  };

  std::array<char, static_cast<std::size_t>(2 * 1024)> arr;

  void toBigEndian()
  {
    for (auto& com : comp)
    {
      com.code4_dist = BSWAP16(com.code4_dist);
      com.code3_dist = BSWAP16(com.code3_dist);
      com.code2_dist = BSWAP16(com.code2_dist);
      com.code1_dist = BSWAP16(com.code1_dist);
    }
  }
};

struct DistAreaU16
{
  int16_t dist;
  uint16_t area;
  void toBigEndian()
  {
    dist = BSWAP16(dist);
    area = BSWAP16(area);
  }
};

struct TwoDimAbsComp
{
  std::array<DistAreaU16, 16> dist_area_arr;
};
struct TwoDimAbsBoard
{
  std::array<TwoDimAbsComp, 8> comp_arr;
};
union TwoDimAbsBit
{
  struct
  {
    std::array<TwoDimAbsBoard, 96> two_dim_abs_board_arr;
    std::array<GdiRegU8, 17> abs_reg;
  };

  void toBigEndian()
  {
    for (auto& board : two_dim_abs_board_arr)
    {
      for (auto& comp : board.comp_arr)
      {
        for (auto& dist_area : comp.dist_area_arr)
        {
          dist_area.toBigEndian();
        }
      }
    }
  }

  std::array<char, static_cast<std::size_t>(48 * 1024 + 256)> arr;
};

constexpr std::array<uint16_t, 16> REFL_CALIB_DIST = { 40,   210,  400,  600,  1400, 2000,  3000,  4000,
                                                       5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000 };

struct AreaComp
{
  uint16_t refl_10;
  uint16_t refl_40;
  uint16_t refl_90;
  uint16_t refl_255;

  void toBigEndian()
  {
    refl_10  = BSWAP16(refl_10);
    refl_40  = BSWAP16(refl_40);
    refl_90  = BSWAP16(refl_90);
    refl_255 = BSWAP16(refl_255);
  }
  uint16_t& getReflChargeComp(const int _refl)
  {
    switch (_refl)
    {
    case 10: return refl_10;
    case 40: return refl_40;
    case 90: return refl_90;
    case 255: return refl_255;
    default: LOG_ERROR("Invalid refl value: {}", _refl); return refl_10;
    }
  }
};

struct ReflChargeComp
{
  std::array<AreaComp, REFL_CALIB_DIST.size()> dist_comp;

  void toBigEndian()
  {
    for (auto& dist : dist_comp)
    {
      dist.toBigEndian();
    }
  }
};

// 经验绝标
struct EmpAbsBit
{
  std::array<char, static_cast<std::size_t>(3 * 1024 + 256)> arr;
};

struct AbsCoeff
{
  uint16_t abs_coe_k;
  uint16_t abs_coe_b;
};

// 绝标
union AbsBit
{
  struct
  {
    std::array<std::array<AbsCoeff, 8>, 96> abs_coe_arr;
    std::array<GdiRegU8, 17> abs_reg;
  };
  std::array<char, static_cast<std::size_t>(3 * 1024 + 256)> arr;

  void toBigEndian()
  {
    for (auto& arr : abs_coe_arr)
    {
      for (auto& coe : arr)
      {
        coe.abs_coe_k = BSWAP16(coe.abs_coe_k);
        coe.abs_coe_b = BSWAP16(coe.abs_coe_b);
      }
    }
  }
};

struct ReflChnComp
{
  std::array<ReflChargeComp, 2> charge;
  void toBigEndian()
  {
    for (auto& comp : charge)
    {
      comp.toBigEndian();
    }
  }
};

// dist_index = [40 250 600 2000 4000 6000 8000 10000 12000 14000 16000 18000 20000 22000 24000 26000];

constexpr uint32_t REF_INDEX_REG_ADDR_START         = 0x8c2101;
constexpr std::array<uint8_t, 4> REFL_INDEX_VALUE   = { 10, 40, 90, 255 };
constexpr uint32_t DIST_INDEX_REG_ADDR_START        = 0x8c2105;
constexpr std::array<uint16_t, 16> DIST_INDEX_VALUE = { 40,   210,  400,  600,  1400, 2000,  3000,  4000,
                                                        5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000 };
constexpr uint32_t AREA_MIN_INDEX_REG_ADDR_START    = 0x8c2125;
constexpr uint16_t AREA_MIN_INDEX_VALUE             = 3250;

constexpr uint32_t PEAK_MIN_INDEX_REG_ADDR_START = 0x8c2127;
constexpr uint16_t PEAK_MIN_INDEX_VALUE          = 500;

constexpr uint32_t REF_CALIB_VERSION_REG_ADDR_START = 0x8c212d;

union ReflBit
{
  struct
  {
    std::array<ReflChnComp, 96> comp_chn;
    std::array<uint8_t, 73728> reserve;  // 96*1024-sizeof(comp_chn)
    std::array<GdiRegU8, REFL_INDEX_VALUE.size()> refl_index_reg;
    std::array<GdiRegU16, DIST_INDEX_VALUE.size()> dist_index_reg;
    GdiRegU16 area_min_index_reg;
    GdiRegU16 peak_min_index_reg;
  };

  std::array<char, static_cast<std::size_t>(97 * 1024)> arr;

  void toBigEndian()
  {
    for (auto& comp : comp_chn)
    {
      comp.toBigEndian();
    }
  }
};
union CombineBit
{
  struct
  {
    ReflBit refl_bit;
    uint16_t refl_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(ReflBit) - sizeof(uint16_t)> refl_reserve;

    DynamicBit dynamic_bit;
    uint16_t dynamic_crc;
    std::array<uint8_t, static_cast<size_t>(256 * 1024) - sizeof(DynamicBit) - sizeof(uint16_t)> dynamic_reserve;

    StaticBit static_bit;
    uint16_t static_crc;
    std::array<uint8_t, static_cast<size_t>(64 * 1024) - sizeof(StaticBit) - sizeof(uint16_t)> static_reserve;

    TwoDimAbsBit two_dim_abs_bit;
    uint16_t two_dim_abs_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(TwoDimAbsBit) - sizeof(uint16_t)> two_dim_abs_reserve;

    AbsBit abs_bit;
    uint16_t abs_crc;
    std::array<uint8_t, static_cast<size_t>(128 * 1024) - sizeof(AbsBit) - sizeof(uint16_t)> abs_reserve;
  };

  void toBigEndian()
  {
    refl_bit.toBigEndian();
    dynamic_bit.toBigEndian();
    static_bit.toBigEndian();
    abs_bit.toBigEndian();
  }

  [[nodiscard]] uint32_t getTwoAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }

  [[nodiscard]] uint32_t getReflOffset() const { return 0; }

  [[nodiscard]] uint32_t getDynamicOffset() const { return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size(); }

  [[nodiscard]] uint32_t getStaticOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size();
  }

  [[nodiscard]] uint32_t getTwoDimAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }

  [[nodiscard]] uint32_t getAbsOffset() const
  {
    return sizeof(ReflBit) + sizeof(uint16_t) + refl_reserve.size() + sizeof(DynamicBit) + sizeof(uint16_t) +
           dynamic_reserve.size() + sizeof(StaticBit) + sizeof(uint16_t) + static_reserve.size();
  }
  std::array<char, static_cast<std::size_t>(704 * 1024)> arr = { 0 };
};

union CustomGdiReg
{
  struct
  {
    GdiRegU16 vbd_err;
    GdiRegU24 vbd_intercept;
    GdiRegU8 refl_calib_version_reg;
  };

  std::array<char, 1024> arr;
};

union CustomGdiFeild
{
  struct
  {
    CustomGdiReg custom_gdi_reg;
    uint16_t gdi_crc;
  };

  std::array<char, 65536> arr;
  [[nodiscard]] static uint32_t getStartAddr() { return 0xfb0000; }
};

#pragma pack(pop)
}  // namespace lidar
}  // namespace robosense
#endif  // DATA_STRUCT_H