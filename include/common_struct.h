﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef COMMON_STRUCT_H
#define COMMON_STRUCT_H

#include <condition_variable>
#include <mutex>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

enum class TestState
{
  NOT_START = 0,
  RUNNING,
  PASS,
  ABORT,
  FAILED
};

struct MsgResult
{
  int index;

  std::mutex mutex;
  std::condition_variable cv;

  bool ret;
};

/*修改为支持状态机的形式*/
enum ActionState
{
  STATE_CHECK_ALL_STATE,
  STATE_CONNECT_LIDAR,
  STATE_ZERO_CHECK_ALL_STATE,
  STATE_ZERO_CONNECT_LIDAR,
  STATE_ZERO_ROTATE,
  STATE_ZERO_COLLECT,
  STATE_ZERO_CALIB,
  STATE_ZERO_TEST,
  STATE_INIT_LIDAR_WAVE,
  STATE_RESET_MOTOR,
  STATE_COLLECT_DATA,
  STATE_SAVE_DATA,
  STATE_SUCCESS,
  STATE_FAIL,
  STATE_ABORT,
  STATE_FINAL,
  STATE_END = -1
};

}  // namespace lidar
}  // namespace robosense
#endif  // COMMON_STRUCT_H