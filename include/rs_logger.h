﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef RS_LOGGER_H
#define RS_LOGGER_H
#include "rsfsc_log/rsfsc_log.h"

#include "rsfsc_log/fmt/magic_enum_format.hpp"

#if __cplusplus >= 201703L
// TODO: make getFuncName all constexpr
#  include <string_view>
constexpr std::string_view getFuncName(std::string_view _pretty_function)
{
  size_t curr_index    = _pretty_function.size() - 1;
  size_t last_brackets = _pretty_function.size() - 1;

  if (_pretty_function.back() == '>')
  {
    while (curr_index > 0)
    {
      curr_index--;
      if (_pretty_function[curr_index] == '<')
      {
        curr_index--;
        break;
      }
    }
  }

  // 找到最后一个括号 '('
  while (curr_index > 0)
  {
    if (_pretty_function[curr_index] == '(')
    {
      last_brackets = curr_index;
      break;
    }
    curr_index--;
  }

  bool colon_found = false;
  while (curr_index > 1)
  {
    if (_pretty_function[curr_index] == ' ')
    {
      return _pretty_function.substr(curr_index + 1, last_brackets - curr_index - 1);
    }
    if (_pretty_function[curr_index] == ':' && _pretty_function[curr_index - 1] == ':')
    {
      if (!colon_found)
      {
        colon_found = true;
      }
      else
      {
        return _pretty_function.substr(curr_index + 1, last_brackets - curr_index - 1);
      }
    }
    curr_index--;
  }

  // 如果没有找到合适的格式，返回整个字符串视图
  return _pretty_function;
}

#else
inline std::string getFuncName(fmt::string_view _pretty_function)
{
  size_t curr_index    = _pretty_function.size() - 1;
  size_t last_brackets = _pretty_function.size() - 1;
  while (curr_index > 0)
  {
    if (_pretty_function[curr_index] == '(')
    {
      last_brackets = curr_index;
      break;
    }
    curr_index--;
  }

  bool colon_found = false;
  while (curr_index > 1)
  {
    if (_pretty_function[curr_index] == ' ')
    {
      return "[" + std::string(_pretty_function.begin() + curr_index + 1, _pretty_function.begin() + last_brackets) +
             "] ";
    }
    if (_pretty_function[curr_index] == ':' && _pretty_function[curr_index - 1] == ':')
    {
      if (!colon_found)
      {
        colon_found = true;
      }
      else
      {
        return "[" + std::string(_pretty_function.begin() + curr_index + 1, _pretty_function.begin() + last_brackets) +
               "] ";
      }
    }
    curr_index--;
  }

  return std::string(_pretty_function.begin(), _pretty_function.end());
}
#endif

// if msvc define pretty function to __FUNCSIG__
#ifdef _MSC_VER
#  define RS_PRETTY_FUNCTION __FUNCSIG__
#else
#  define RS_PRETTY_FUNCTION __PRETTY_FUNCTION__
#endif

// 基础日志格式化宏
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#ifdef RSFSCLOG_SHOW_FUNCNAME
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#  define RS_LOG_FORMAT(...) fmt::format("[{}] {}", getFuncName(RS_PRETTY_FUNCTION), fmt::format(__VA_ARGS__))
#else
#  define RS_LOG_FORMAT(...) __VA_ARGS__
#endif

// 基础日志宏
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define RS_LOG_BASE(logger, level, ...) logger->level(RS_LOG_FORMAT(__VA_ARGS__))

// 重新定义日志宏
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INFO(...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), info, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_DEBUG(...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), debug, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_ERROR(...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), error, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_TRACE(...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), trace, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_WARN(...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), warn, ##__VA_ARGS__)

#ifdef RSFSCLOG_INDEX_SUPPORT
// 带索引的日志宏
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#  define LOG_INDEX_BASE(level, ...) \
    RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(getLogIndex()), level, ##__VA_ARGS__)
#else
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#  define LOG_INDEX_BASE(level, ...) RS_LOG_BASE(robosense::lidar::RSFSCLog::getInstance(), level, ##__VA_ARGS__)
#endif

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INDEX_INFO(...) LOG_INDEX_BASE(info, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INDEX_ERROR(...) LOG_INDEX_BASE(error, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INDEX_DEBUG(...) LOG_INDEX_BASE(debug, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INDEX_WARN(...) LOG_INDEX_BASE(warn, ##__VA_ARGS__)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define LOG_INDEX_TRACE(...) LOG_INDEX_BASE(trace, ##__VA_ARGS__)

#endif  // RS_LOGGER_H