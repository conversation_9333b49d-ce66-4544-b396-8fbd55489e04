﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "lidar_ctl.h"
#include "app_event.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "model/airy_wave_signal_model.h"
#include "rs_logger.h"
#include "rsfsc_log/rsfsc_log.h"

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

int CheckAllState::handleState()
{
  getWorkModel()->updateAreaType(AiryWaveSignalModel::AreaType::UNKNOWN);
  if (!getWorkModel()->checkAllState())
  {
    LOG_ERROR("checkAllState fail");
    return STATE_END;
  }

  auto version = getWorkModel()->getLidarManager()->getVersion();

  app()->getWidgetLogSetting()->setFirmwareRevision(
    getWorkModel()->getLidarIndex(), static_cast<int>(version.ps_version), static_cast<int>(version.pl_version));
  app()->signalUpdateTestState(TestState::RUNNING);

  if (getParaInfo()->getCheckFirmwareVersion())
  {
    if (!getWorkModel()->checkVersion())
    {
      app()->signalShowErrorText("版本检查失败");
      return STATE_FAIL;
    }
  }

  uint32_t curr_status = 0;
  if (!getLidarManager()->readTopRegDataByKey("calib_status", curr_status, 1) &&
      !getLidarManager()->readTopRegDataByKey("calib_status", curr_status, 1))
  {
    getWorkModel()->setFailMsg(className(), "读取标定状态失败");
  }
  auto vbd_crc = (curr_status >> 4U) & 0x1U;
  if (!getWorkModel()->addMeasureMessage("vbd_crc_status", vbd_crc, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    getWorkModel()->setFailMsg(className(), "校验vbd位失败");
    app()->signalShowErrorText("vbd校验失败");
    return STATE_FAIL;
  }

  if (!getWorkModel()->checkIfChnAngleZero())
  {
    getWorkModel()->setFailMsg(className(), "通道角度为0，请先写入通道角度再进行标定");
    app()->signalShowErrorText("通道角度为0，请先写入通道角度再进行标定");
    return STATE_FAIL;
  }

  LOG_DEBUG("success");
  return STATE_ZERO_ROTATE;
}

int ConnectLidar::handleState()
{
  if (!extDevice()->turnRelay(getLidarIndex(), true))
  {
    LOG_INDEX_ERROR("打开继电器失败");
  }

  // if (!getWorkModel()->scanConnectLidarAndWaitForTop())
  // {
  //   getWorkModel()->setFailMsg(className(), "扫描局域网雷达失败");
  //   return STATE_ABORT;
  // }

  if (!getWorkModel()->addMeasureMessage("fsm_connect_lidar", getWorkModel()->scanConnectLidarAndWaitForTop()))
  {
    getWorkModel()->setFailMsg(className(), "连接雷达失败");
    return STATE_ABORT;
  }
  LOG_DEBUG("success");

  if (getLastMachineState() == STATE_SAVE_DATA)
  {
    return STATE_ZERO_ROTATE;
  }

  return STATE_CHECK_ALL_STATE;
}

int ZeroRotate::handleState()
{
  if (!getWorkModel()->getIsCollectWave() && !getParaInfo()->getOneKeyFirstZero())
  {
    LOG_INDEX_INFO("跳过一次零度角标定");
    return STATE_INIT_LIDAR_WAVE;
  }

  if (!getParaInfo()->getZeroRotate())
  {
    LOG_INDEX_INFO("跳过零度角旋转转台");
    return STATE_ZERO_ROTATE;
  }

  if (!getWorkModel()->getLidarManager()->setZeroAngle(0))
  {
    getWorkModel()->setFailMsg(className(), "连续两次次清除零度角失败");
    return STATE_FAIL;
  }

  app()->signalUpdateZeroAngle(0);
  LOG_INFO("set zero angle : 0");

  if (!extDevice()->setRotatorXSpeed(getParaInfo()->getRotateDefaultSpeed()))
  {
    getWorkModel()->addMeasureMessage("fsm_zero_rotate", false);
    getWorkModel()->setFailMsg(className(), "设置转台速度失败");
    return STATE_FAIL;
  }

  auto zero_pos = getParaInfo()->getZeroPos().toFloat();
  if (!extDevice()->setRotatorXAngle(zero_pos))
  {
    LOG_ERROR("setRotatorXAngle failed.");
    getWorkModel()->addMeasureMessage("fsm_reset_motor", false);
    getWorkModel()->setFailMsg(className(), "零度角设置状态失败");
    return STATE_FAIL;
  }
  LOG_INFO("转台X, [{}°] 移动中...", zero_pos);
  if (getParaInfo()->getInitLidar())
  {
    if (!getWorkModel()->initZeroCalib())
    {
      return STATE_FAIL;
    }
  }

  while (!isAbort())
  {
    sleep(2);
    bool is_moving = false;
    if (!extDevice()->isRotatorXMoving(is_moving))
    {
      getWorkModel()->setFailMsg(className(), "转台状态查询失败");
      return STATE_FAIL;
    }
    if (!is_moving)
    {
      break;
    }
  }

  LOG_DEBUG("success");
  return STATE_ZERO_COLLECT;
}

int ZeroCollect::handleState()
{
  if (!getWorkModel()->startCollectZero())
  {
    LOG_ERROR("startCollectZero...失败");
    return STATE_FAIL;
  }

  LOG_INFO("开始采集数据zero");

  sleep(5);

  getWorkModel()->stopCollectPcap();
  LOG_INFO("采集结束zero");

  LOG_DEBUG("success");
  return STATE_ZERO_CALIB;
}

int ZeroCalib::handleState()
{
  if (!getWorkModel()->loadPcapZero())
  {
    getWorkModel()->setFailMsg(className(), "加载pcap文件失败");
    return STATE_FAIL;
  }

  if (!getWorkModel()->processZero())
  {
    getWorkModel()->setFailMsg(className(), "零度角处理失败");
    return STATE_FAIL;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_zero_angle", getWorkModel()->getZeroAngle(),
                                         rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    getWorkModel()->setFailMsg(className(), "零度角阈值超限");
    return STATE_FAIL;
  }

  if (!getLidarManager()->setZeroAngle(getWorkModel()->getZeroAngle()))
  {
    getWorkModel()->setFailMsg(className(), "写入零度角失败");
    return STATE_FAIL;
  }
  sleep(1);

  LOG_DEBUG("success");
  return STATE_ZERO_TEST;
}

int ZeroTest::handleState()
{
  if (!getWorkModel()->startCollectZero(true))
  {
    getWorkModel()->setFailMsg(className(), "startCollectZero_test...失败");
    return STATE_FAIL;
  }

  LOG_INFO("开始采集数据zero_test");

  sleep(5);

  getWorkModel()->stopCollectPcap();

  LOG_INFO("采集结束zero_test");
  msleep(500);

  if (!getWorkModel()->loadPcapZero(true))
  {
    getWorkModel()->setFailMsg(className(), "加载pcap文件失败");
    return STATE_FAIL;
  }

  if (!getWorkModel()->processZeroTest())
  {
    getWorkModel()->setFailMsg(className(), "零度角校验失败");
    return STATE_FAIL;
  }

  LOG_DEBUG("success");

  if (getWorkModel()->getIsCollectWave())
  {
    if (getWorkModel()->getAreaType() != AiryWaveSignalModel::AreaType::PASS)
    {
      LOG_INDEX_ERROR("积分值: {}，失败", getWorkModel()->getAreaType());
      return STATE_FAIL;
    }
    return STATE_SUCCESS;
  }

  if (!getParaInfo()->getOneKeyWave())
  {
    return STATE_SUCCESS;
  }

  return STATE_INIT_LIDAR_WAVE;
}

int InitLidarWave::handleState()
{
  if (!getParaInfo()->getInitLidar())
  {
    LOG_INFO("跳过初始化雷达");
    return STATE_RESET_MOTOR;
  }

  if (!getWorkModel()->initWaveCalib())
  {
    return STATE_FAIL;
  }

  LOG_DEBUG("success");
  return STATE_RESET_MOTOR;
}

int ResetMotor::handleState()
{
  if (!getParaInfo()->getOneKeyWave())
  {
    return STATE_SUCCESS;
  }

  if (getParaInfo()->getWaveAngleConfirm())
  {
    float light_spot_confirm_angle = getParaInfo()->getRotateLightSpot();
    LOG_INFO("正移动至光斑检查角: {}", light_spot_confirm_angle);
    if (!extDevice()->setRotatorXSpeed(getParaInfo()->getRotateDefaultSpeed()))
    {
      getWorkModel()->setFailMsg(className(), "设置转台速度失败");
      return STATE_FAIL;
    }

    if (!extDevice()->setRotatorXAngle(light_spot_confirm_angle))
    {
      getWorkModel()->setFailMsg(className(), "转台设置光斑检查角度失败");
      return STATE_FAIL;
    }
    while (!isAbort())
    {
      bool is_moving = false;
      if (!extDevice()->isRotatorXMoving(is_moving))
      {
        getWorkModel()->setFailMsg(className(), "转台查询状态失败");
        return STATE_FAIL;
      }
      if (!is_moving)
      {
        break;
      }
    }
    MsgResult result;
    std::unique_lock<std::mutex> lock(result.mutex);
    app()->signalMsgBoxConfirmSpotAngle(&result);
    result.cv.wait(lock);
    if (!result.ret)
    {
      getWorkModel()->setFailMsg(className(), "光斑检查角度确认失败");
      return STATE_FAIL;
    }
  }

  LOG_INFO("当前参数: 起始位置: {}, 结束位置: {}", getParaInfo()->getRotatorXAngleStart(),
           getParaInfo()->getRotatorXAngleEnd());
  auto start_angle = getParaInfo()->getRotatorXAngleStart();
  if (!extDevice()->setRotatorXAngle(static_cast<float>(start_angle)))
  {
    getWorkModel()->setFailMsg(className(), "转台设置起始位置失败");
    getWorkModel()->addMeasureMessage("fsm_control_rotator", false);
    return STATE_FAIL;
  }

  if (!extDevice()->setRotatorXSpeed(getParaInfo()->getRotateDefaultSpeed()))
  {
    getWorkModel()->addMeasureMessage("fsm_reset_motor", false);
    getWorkModel()->setFailMsg(className(), "设置转台转速失败");
    return STATE_FAIL;
  }

  while (!isAbort())
  {
    bool is_moving = false;
    float angle    = 0.0;
    if (!extDevice()->isRotatorXMoving(is_moving))
    {
      LOG_ERROR("获取转台状态失败");
      getWorkModel()->addMeasureMessage("fsm_reset_motor", false);
      getWorkModel()->setFailMsg(className(), "获取转台转台失败");
      return STATE_FAIL;
    }
    if (!extDevice()->getRotatorXAngle(angle))
    {
      // LOG_ERROR("获取转台角度失败");
      // return STATE_FAIL;
    }

    LOG_INFO("等待转台移动到起始位置...  当前状态: {}, 角度: {}", (is_moving ? "移动中" : "已停止"), angle);
    if (!is_moving)
    {
      break;
    }
    msleep(2000);
  }

  if (!extDevice()->setRotatorXSpeed(getParaInfo()->getRotatorXSpeedStart()))
  {
    getWorkModel()->addMeasureMessage("fsm_reset_motor", false);
    getWorkModel()->setFailMsg(className(), "设置转台转速失败");
    return STATE_FAIL;
  }
  LOG_INFO("转台X速度设置为 {} ", getParaInfo()->getRotatorXSpeedStart());

  LOG_DEBUG("success");
  getWorkModel()->addMeasureMessage("fsm_reset_motor", true);
  return STATE_COLLECT_DATA;
}

int CollectData::handleState()
{
  auto spinbox_rotator_x_angle_end   = getParaInfo()->getRotatorXAngleEnd();
  auto spinbox_rotator_x_angle_start = getParaInfo()->getRotatorXAngleStart();
  auto total_task = static_cast<int>((spinbox_rotator_x_angle_end - spinbox_rotator_x_angle_start) * 100);
  app()->signalUpdateProgressCollectTotalTask(total_task);
  if (!extDevice()->setRotatorXAngle(spinbox_rotator_x_angle_end))
  {
    getWorkModel()->addMeasureMessage("fsm_collect_data", false);
    getWorkModel()->setFailMsg(className(), "转台X设置失败，转台X终点角度设置失败");
    return STATE_FAIL;
  }
  LOG_INFO("转台X, [{}°] 移动中...", spinbox_rotator_x_angle_end);

  if (!getWorkModel()->startCollectPcap())
  {
    getWorkModel()->addMeasureMessage("fsm_collect_data", false);
    getWorkModel()->setFailMsg(className(), "采集初始化失败");
    return STATE_FAIL;
  }

  LOG_INFO("开始采集数据");
  while (!isAbort())
  {
    bool is_moving = false;
    float angle    = 0.0;
    if (!extDevice()->isRotatorXMoving(is_moving))
    {
      getWorkModel()->addMeasureMessage("fsm_control_rotator", false);
      getWorkModel()->setFailMsg(className(), "查询转台状态失败，无法判断是否停止");
      return STATE_FAIL;
    }

    if (!extDevice()->getRotatorXAngle(angle))
    {
      // return STATE_FAIL;
    }

    if (!is_moving)
    {
      break;
    }

    app()->signalUpdateProgressCollect(static_cast<int>((angle - spinbox_rotator_x_angle_start) * 100));
  }

  getWorkModel()->stopCollectPcap();

  getWorkModel()->setCollectWave(true);

  LOG_INFO("采集结束");
  app()->signalUpdateProgressCollect(total_task);

  if (getParaInfo()->getDeinitLidar())
  {
    if (!getLidarManager()->writeCsvDataAfterCalib("init"))
    {
      getWorkModel()->addMeasureMessage("fsm_collect_data", false);
      getWorkModel()->setFailMsg(className(), "采集后，恢复寄存器数据失败");
      return STATE_FAIL;
    }
    if (!getLidarManager()->writeCsvCmdDataAfterCalib("init_cmd"))
    {
      getWorkModel()->addMeasureMessage("fsm_collect_data", false);
      getWorkModel()->setFailMsg(className(), "采集后，恢复寄存器数据失败");
      return STATE_FAIL;
    }
  }

  LOG_DEBUG("success");
  getWorkModel()->addMeasureMessage("fsm_collect_data", true);
  return STATE_SAVE_DATA;
}

int SaveData::handleState()
{
  LOG_DEBUG("success");
  bool is_success = true;
  if (!getWorkModel()->addMeasureMessage("fsm_load_pcap", getWorkModel()->loadPcapFile()))
  {
    LOG_ERROR("loadPcapFile...fail");
    getWorkModel()->setFailMsg(className(), "加载pcap文件失败");
    return STATE_FAIL;
  }
  if (!getWorkModel()->addMeasureMessage("fsm_auto_process", getWorkModel()->autoProcessAllData()))
  {
    LOG_ERROR("autoProcessAllData...fail");
    getWorkModel()->setFailMsg(className(), "处理数据出现NG项");
    return STATE_FAIL;
  }
  if (!getWorkModel()->addMeasureMessage("fsm_write_calib_data", getWorkModel()->writeBitToLidar()))
  {
    LOG_ERROR("write bit fail");
    getWorkModel()->setFailMsg(className(), "写入标定数据失败");
    return STATE_FAIL;
  }

  if (getParaInfo()->getOneKeySecZero())
  {
    extDevice()->turnRelay(getParaInfo()->getRelayChn(), false);
    sleep(2);
    extDevice()->turnRelay(getParaInfo()->getRelayChn(), true);
    if (getParaInfo()->getZeroConfirm())
    {
      MsgResult result;
      std::unique_lock<std::mutex> lock(result.mutex);
      app()->signalMsgBoxConfirm("回波信号标定完成，确认雷达重启后标定零度角", &result);
      result.cv.wait(lock);
      if (!result.ret)
      {
        getWorkModel()->setFailMsg(className(), "光斑检查角度确认失败");
        return STATE_FAIL;
      }
    }

    return STATE_CONNECT_LIDAR;
  }

  return STATE_SUCCESS;
}

int FailState::handleState()
{
  LOG_INFO("fail");
  getWorkModel()->finishProcess(rsfsc_lib::LogTestStatus::LOG_TEST_STATUS_FAIL);
  app()->signalUpdateTestState(TestState::FAILED);
  return STATE_FINAL;
}

int AbortState::handleState()
{
  LOG_INFO("abort");
  getWorkModel()->finishProcess(rsfsc_lib::LogTestStatus::LOG_TEST_STATUS_ABORT);
  app()->signalUpdateTestState(TestState::ABORT);
  return STATE_FINAL;
}

int SuccessState::handleState()
{
  LOG_INFO("success");
  getWorkModel()->finishProcess(rsfsc_lib::LogTestStatus::LOG_TEST_STATUS_PASS);
  app()->signalUpdateTestState(TestState::PASS);
  return STATE_FINAL;
}

int FinalState::handleState()
{
  LOG_INFO("final");
  extDevice()->turnRelay(getLidarIndex(), false);
  extDevice()->setRotatorXSpeed(getParaInfo()->getRotateDefaultSpeed());
  extDevice()->setRotatorXAngle(getParaInfo()->getRotateSetupLidar());

  return STATE_END;
}

}  // namespace lidar
}  // namespace robosense
