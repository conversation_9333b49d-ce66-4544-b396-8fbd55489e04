/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "model/airy_wave_signal_model.h"
#include "app_event.h"
// #include "matplotlibcpp.h"
#include "mech_comm_func/mes.h"
#include "model/analyze_utils.hpp"
#include "model/data_struct.h"
#include "model/poly_fit.hpp"
#include "rs_logger.h"
#include "utils/common.h"
#include "utils/pcap_utils.h"
#include <QDateTime>
#include <QDir>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QProcess>
#include <QTextStream>
#include <QtCore/QFile>
#include <QtCore/QTextStream>
#include <algorithm>
#include <bitset>
#include <csignal>
#include <set>
#include <sys/wait.h>
#include <utils/crc_utils.h>
#include <utils/thread_ext.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

// 10%的延伸面积参数为：[3103 2131 1464 1006 691 474 326 224 154]
// 40%的延伸面积参数为：[9613 7253 5414 4028 3027 2340 1900 1637 1483]
// 90%的延伸面积参数为：[13688 11507 9609 7974 6580 5408 4438 3648 3019]
constexpr std::array<uint16_t, 9> REFL10_INTERPOLATION_VAL = { 3103, 2131, 1464, 1006, 691, 474, 326, 224, 154 };
constexpr std::array<uint16_t, 9> REFL40_INTERPOLATION_VAL = { 9613, 7253, 5414, 4028, 3027, 2340, 1900, 1637, 1483 };
constexpr std::array<uint16_t, 9> REFL90_INTERPOLATION_VAL = { 13688, 11507, 9609, 7974, 6580, 5408, 4438, 3648, 3019 };
constexpr std::array<uint16_t, 9> REFL_INTERPOLATION_DIST = { 4000, 5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000 };

// float getReflInterpolationK(const std::array<uint16_t, 9>& _interpolation_val, const size_t _index)
// {
//   return static_cast<float>(_interpolation_val.at(_index)) / static_cast<float>(_interpolation_val.at(0));
// }

static void fromJson(ReflBoardPos& _refl_board_pos, const QJsonObject& _json_obj)
{
  auto key_list = _json_obj.keys();
  for (const auto& key : key_list)
  {
    if (key == "refl")
    {
      _refl_board_pos.refl = _json_obj[key].toInt();
    }
    else if (key == "distance")
    {
      _refl_board_pos.distance = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "width")
    {
      _refl_board_pos.width = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "pos_factor")
    {
      _refl_board_pos.pos_factor = static_cast<float>(_json_obj[key].toDouble());
    }
  }
}

static void fromJson(BoardInfo& _board_info, const QJsonObject& _json_obj)
{
  // find json_obj key
  auto key_list = _json_obj.keys();
  for (const auto& key : key_list)
  {
    if (key == "board_id")
    {
      _board_info.board_id = key.toInt();
    }
    else if (key == "raw_dist_min")
    {
      _board_info.raw_dist_min = _json_obj[key].toInt();
    }
    else if (key == "raw_dist_max")
    {
      _board_info.raw_dist_max = _json_obj[key].toInt();
    }
    else if (key == "comped_dist_min")
    {
      _board_info.comped_dist_min = _json_obj[key].toInt();
    }
    else if (key == "comped_dist_max")
    {
      _board_info.comped_dist_max = _json_obj[key].toInt();
    }
    else if (key == "board_index_min")
    {
      _board_info.board_index_min = _json_obj[key].toInt();
    }
    else if (key == "board_index_max")
    {
      _board_info.board_index_max = _json_obj[key].toInt();
    }
    else if (key == "distance_05cm")
    {
      _board_info.distance_05cm = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "distance")
    {
      _board_info.distance = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "width")
    {
      _board_info.width = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "board_start_angle")
    {
      _board_info.board_start_angle = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "board_end_angle")
    {
      _board_info.board_end_angle = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "board_index_length")
    {
      _board_info.board_index_length = _json_obj[key].toInt();
    }
    else if (key == "board_start_area_min")
    {
      _board_info.board_start_area_min = _json_obj[key].toInt();
    }
    else if (key == "board_end_area_min")
    {
      _board_info.board_end_area_min = _json_obj[key].toInt();
    }
    else if (key == "board_start_remove_factor")
    {
      _board_info.board_start_remove_factor = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "board_end_remove_factor")
    {
      _board_info.board_end_remove_factor = static_cast<float>(_json_obj[key].toDouble());
    }
    else if (key == "refl_board_vec")
    {
      auto refl_board_vec_json = _json_obj[key].toArray();
      for (const auto& refl_board_json : refl_board_vec_json)
      {
        auto refl_board_pos_json = refl_board_json.toObject();
        ReflBoardPos refl_board_pos;
        fromJson(refl_board_pos, refl_board_pos_json);
        _board_info.refl_board_vec.push_back(refl_board_pos);
      }
    }
  }
}

AiryWaveSignalModel::AiryWaveSignalModel(WidgetLidarInfo* _widget_lidar_info) : WorkModel(_widget_lidar_info)
{
  loadConfigData();
}
void AiryWaveSignalModel::loadConfigData()
{
  limit_csv_utils_ptr_ = app()->getCsvUtils("airy_limit");
  if (limit_csv_utils_ptr_ == nullptr)
  {
    LOG_ERROR("获取limit csv解析器失败");
    return;
  }
  getLidarManager()->loadConfigData();
  loadJsonData();
}

AiryWaveSignalModel::~AiryWaveSignalModel() = default;

void AiryWaveSignalModel::resetState() { zero_calib_count_ = 0; }

bool AiryWaveSignalModel::addMeasureMessage(const QString& _name, const bool _data)
{
  return addMeasureMessage(_name, _data ? 1 : 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
}
bool AiryWaveSignalModel::addMeasureMessage(const QString& _name,
                                            const double _data,
                                            const rsfsc_lib::MeasureDataType _data_type)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());
  return addMeasureMessage(limit_info, _data, _data_type);
}
bool AiryWaveSignalModel::addMeasureMessage(const LimitInfo& _limit_info,
                                            const double _data,
                                            const rsfsc_lib::MeasureDataType _data_type)
{
  if (!_limit_info.is_ok)
  {
    LOG_ERROR("未找到该limit信息 name: {}", _limit_info.name);
    return false;
  }
  if (!app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, _data, _data_type))
  {
    if (_data_type == rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT)
    {
      LOG_INDEX_ERROR("{}{} 当前值{}阈值超限, 下限{}, 上限{}", _limit_info.name, _limit_info.getNameSuffix(),
                      static_cast<int>(_data), _limit_info.min_th, _limit_info.max_th);
    }
    else if (_data_type == rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_HEX)
    {
      LOG_INDEX_ERROR("{}{} 当前值{:#x}阈值超限, 下限{:#x}, 上限{:#x}", _limit_info.name, _limit_info.getNameSuffix(),
                      static_cast<uint32_t>(_data), static_cast<uint32_t>(_limit_info.min_th),
                      static_cast<uint32_t>(_limit_info.max_th));
    }
    else
    {
      LOG_INDEX_ERROR("{}{} 当前值{:.3f}阈值超限, 下限{:.3f}, 上限{:.3f}", _limit_info.name,
                      _limit_info.getNameSuffix(), _data, _limit_info.min_th, _limit_info.max_th);
    }

    return false;
  }
  return true;
}
bool AiryWaveSignalModel::addMeasureMessage(const QString& _name, const std::string& _data)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());
  if (!limit_info.is_ok)
  {
    LOG_ERROR("未找到该limit信息 name: {}", _name);
    return false;
  }
  return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data);
}
bool AiryWaveSignalModel::addMeasureMessageWithSuffix(const QString& _name, const QString& _suffix, const double _data)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());
  if (!limit_info.is_ok)
  {
    LOG_ERROR("未找到该limit信息 name: {}", _name);
    return false;
  }
  limit_info.setNameSuffix(_suffix.toStdString());
  return addMeasureMessage(limit_info, _data, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
}

void AiryWaveSignalModel::setFailMsg(const QString& _fail_label, const QString& _fail_msg)
{
  fail_label_ = _fail_label;
  fail_msg_   = _fail_msg;
  LOG_ERROR("{}, fail_msg: {}", fail_label_, fail_msg_);
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo("fsm_fail");
  limit_info.extra_str_info[0] += _fail_msg.toStdString();
  app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, 1,
                                                  rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
}

void AiryWaveSignalModel::finishProcess(const rsfsc_lib::LogTestStatus _ts)
{
  app()->getWidgetLogSetting()->setTestStatus(getLidarIndex(), _ts, fail_label_, fail_msg_);
  app()->getWidgetLogSetting()->finishProcess(getLidarIndex(), fail_msg_);
}

void AiryWaveSignalModel::loadJsonData()
{
  auto json_board_info = app()->getJsonBoardInfo();
  if (json_board_info.isEmpty())
  {
    LOG_INDEX_ERROR("json_board_info is null");
    app()->signalShowErrorText("json_board_info is null");
    return;
  }
  auto board_json = json_board_info[pcap_factory_loc_].toObject();

  auto refl_board_id_vec_json = board_json["refl_board_id_vec"].toArray();
  auto abs_board_id_vec_json  = board_json["abs_board_id_vec"].toArray();
  board_3m_id_                = board_json["board_3m_id"].toInt();
  board_10m_id_               = board_json["board_10m_id"].toInt();
  board_20m_id_               = board_json["board_20m_id"].toInt();
  board_1_2m_id_              = board_json["board_1_2m_id"].toInt();
  board_0_2m_id_              = board_json["board_0_2m_id"].toInt();
  static_board_id_            = board_json["static_board_id"].toInt();
  dynamic_board_id_           = board_json["dynamic_board_id"].toInt();
  refl_board_id_vec_.clear();
  abs_board_id_vec_.clear();
  for (const auto& board_id : refl_board_id_vec_json)
  {
    refl_board_id_vec_.push_back(board_id.toInt());
  }
  for (const auto& board_id : abs_board_id_vec_json)
  {
    abs_board_id_vec_.push_back(board_id.toInt());
  }
  std::map<BoardIDType, BoardInfo> board_info_map;
  auto board_info_map_json = board_json["board_info_map"].toObject();
  // for (auto it = board_info_map_json.begin(); it != board_info_map_json.end(); ++it)
  // {
  //   int board_id         = it.key().toInt();
  //   auto board_info_json = it.value().toObject();
  //   BoardInfo board_info;
  //   fromJson(board_info, board_info_json);
  //   board_info.board_id = board_id;
  // }
  for (auto it = board_info_map_json.begin(); it != board_info_map_json.end(); ++it)
  {
    int board_id         = it.key().toInt();
    auto board_info_json = it.value().toObject();
    BoardInfo board_info;
    fromJson(board_info, board_info_json);
    board_info.board_id = board_id;
    if (board_info.distance_05cm <= 0 && board_info.distance > 0)
    {
      board_info.distance_05cm = board_info.distance / 5;
    }
    board_info_map[board_id] = board_info;
  }
  default_board_info_map_ = board_info_map;

  auto normal_json                = json_board_info["normal"].toObject();
  dynamic_area_min_               = normal_json["dynamic_area_min"].toInt();
  dynamic_area_max_               = normal_json["dynamic_area_max"].toInt();
  dynamic_remove_low_refl_factor_ = normal_json["dynamic_remove_low_refl_factor"].toDouble();
  block_3m_refl_delta_vec_.clear();
  if (mount_type_ == mech::MountType::MAPPING)
  {
    dynamic_area_max_ = normal_json["MAPPING"].toObject()["dynamic_area_max"].toInt();
    LOG_INDEX_INFO("检测到测绘版，动标高反筛选阈值为: {}", dynamic_area_max_);
  }
  if (mount_type_ == mech::MountType::FRONT || mount_type_ == mech::MountType::SIDE)
  {
    QString mount_type_str = fmt::format("{}", mount_type_).c_str();
    auto mount_json        = normal_json[mount_type_str].toObject();
    for (const auto& delta : mount_json["block_3m_refl_delta"].toArray())
    {
      block_3m_refl_delta_vec_.push_back(delta.toInt());
    }
  }
  else
  {
    for (const auto& delta : normal_json["block_3m_refl_delta"].toArray())
    {
      block_3m_refl_delta_vec_.push_back(delta.toInt());
    }
  }

  if (block_3m_refl_delta_vec_.size() < 12)
  {
    LOG_INDEX_ERROR("加载3m高反配置数据失败, 当前长度小于12: {}", block_3m_refl_delta_vec_);
    app()->signalShowErrorText("加载3m高反配置数据失败");
  }
  else
  {
    LOG_INDEX_INFO("加载到3m高反数据成功type: {}, data: {}", mount_type_, block_3m_refl_delta_vec_);
  }
  if (dynamic_area_min_ == 0 || dynamic_area_max_ == 0)
  {
    LOG_INDEX_ERROR("动态靶板参数加载失败 dynamic_area_min_ {}, dynamic_area_max_ {}", dynamic_area_min_,
                    dynamic_area_max_);
    app()->signalShowErrorText("动态靶板参数加载失败");
  }

  // 零度角参数
  auto zero_json      = json_board_info["zero"].toObject();
  zero_dist_min_      = zero_json["dist_min"].toInt() / 5;
  zero_dist_max_      = zero_json["dist_max"].toInt() / 5;
  zero_high_refl_min_ = zero_json["high_refl_min"].toInt();
  if (zero_dist_min_ == 0 || zero_dist_max_ == 0 || zero_high_refl_min_ == 0)
  {
    LOG_INDEX_ERROR("零度角参数加载失败 zero_dist_min_ {}, zero_dist_max_ {}, zero_high_refl_min_ {}", zero_dist_min_,
                    zero_dist_max_, zero_high_refl_min_);
    app()->signalShowErrorText("零度角参数加载失败");
  }
}

std::map<int, BoardInfo> AiryWaveSignalModel::getDefaultBoardInfoMap()
{
  loadJsonData();
  return default_board_info_map_;
}
std::map<int, BoardInfo> AiryWaveSignalModel::initBoardInfo(const size_t _data_size)
{
  // 记录除以5为真实距离，单位是5mm
  int factor          = static_cast<int>(std::round((static_cast<float>(sample_interval_) * rotator_speed_factor_)));
  int board_id        = 0;
  float rotator_start = getParaInfo()->getRotatorXAngleStart();
  float rotator_end   = getParaInfo()->getRotatorXAngleEnd();
  float rotate_angle  = rotator_end - rotator_start;

  std::map<BoardIDType, BoardInfo> board_info_map = getDefaultBoardInfoMap();

  for (auto& [key, board_info] : board_info_map)
  {
    board_info.board_index_length =
      static_cast<int>(0.8 * static_cast<float>(_data_size) *
                       (board_info.board_end_angle - board_info.board_start_angle) / rotate_angle);
    float curr_width  = 0;
    float total_width = board_info.width;
    for (auto& refl_board_pos : board_info.refl_board_vec)
    {
      auto refl_board_center_width = refl_board_pos.width / 2;
      curr_width += refl_board_center_width;
      if (refl_board_pos.pos_factor < 0)
      {
        refl_board_pos.pos_factor = curr_width / total_width;
      }

      curr_width += refl_board_center_width;
      if (refl_board_pos.distance_05cm < 0 && refl_board_pos.distance > 0)
      {
        refl_board_pos.distance_05cm = refl_board_pos.distance / 5;
      }
    }
  }

  return board_info_map;
}

bool AiryWaveSignalModel::scanConnectLidarAndWaitForTop()
{
  auto is_succ = getLidarManager()->scanFirstLidarAndSetIP() && connectLidar();
  if (auto mount_type = getLidarManager()->getMountType())
  {
    mount_type_ = *mount_type;
    if (!magic_enum::enum_contains<mech::MountType>(mount_type_))
    {
      app()->signalShowWarningText(fmt::format("警告: 识别机器的安装类型失败, 当前类型: {}", mount_type_).c_str());
      LOG_INDEX_WARN("警告: 识别机器的安装类型失败");
    }
    LOG_INDEX_INFO("识别到雷达安装类型: {}", mount_type_);
  }
  else
  {
    LOG_INDEX_ERROR("获取安装方式失败");
    return false;
  }
  return is_succ;
}
bool AiryWaveSignalModel::connectLidar() { return getLidarManager()->connect(); }
bool AiryWaveSignalModel::disconnectLidar() { return getLidarManager()->disconnect(); }

bool AiryWaveSignalModel::checkAllState()
{
  QString data_dir;
  QString temp_dir;
  QString result_dir;

  if (getLidarManager()->getLidarInfo()->getLidarSN().isEmpty())
  {
    app()->signalShowErrorText("雷达SN为空");
    return false;
  }

  auto check_state = app()->getWidgetLogSetting()->checkAllState(getLidarIndex(), data_dir, temp_dir, result_dir);

  switch (check_state)
  {
  case rsfsc_lib::CHECK_STATE_SUCCESS:
  {
    break;
  }
  case rsfsc_lib::CHECK_STATE_ITEM_EMPTY:
  default:
  {
    LOG_ERROR("checkAllState failed {}", check_state);
    return false;
  }
  }

  path_.data_dir   = QDir(data_dir);
  path_.temp_dir   = QDir(temp_dir);
  path_.result_dir = QDir(result_dir);
  initPath();

  zero_calib_count_ = 0;
  is_collect_wave_  = false;
  updateAreaType(AreaType::UNKNOWN);
  return true;
}

bool AiryWaveSignalModel::checkVersion()
{
  if (auto mes_ver = mech::requireCustomerSN(app()->getWidgetLogSetting(), getLidarIndex()))
  {
    auto limit_top_ver    = limit_csv_utils_ptr_->getLimitInfo("top_firmware_version");
    auto limit_bot_ver    = limit_csv_utils_ptr_->getLimitInfo("bot_firmware_version");
    auto limit_motor_ver  = limit_csv_utils_ptr_->getLimitInfo("motor_firmware_version");
    auto limit_app_ver    = limit_csv_utils_ptr_->getLimitInfo("app_firmware_version");
    auto limit_config_ver = limit_csv_utils_ptr_->getLimitInfo("config_version");

    limit_top_ver.min_th    = mes_ver->top_version;
    limit_bot_ver.min_th    = mes_ver->bot_version;
    limit_motor_ver.min_th  = mes_ver->motor_version;
    limit_app_ver.min_th    = mes_ver->app_version;
    limit_config_ver.min_th = mes_ver->config_version;

    limit_top_ver.max_th    = mes_ver->top_version;
    limit_bot_ver.max_th    = mes_ver->bot_version;
    limit_motor_ver.max_th  = mes_ver->motor_version;
    limit_app_ver.max_th    = mes_ver->app_version;
    limit_config_ver.max_th = mes_ver->config_version;

    auto lidar_ver = getLidarManager()->getVersion();

    if (!addMeasureMessage(limit_top_ver, lidar_ver.pl_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setFailMsg("top_firmware_version", "版本号不满足要求");
      return false;
    }
    if (!addMeasureMessage(limit_bot_ver, lidar_ver.ps_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setFailMsg("bot_firmware_version", "版本号不满足要求");
      return false;
    }
    if (!addMeasureMessage(limit_motor_ver, lidar_ver.motor_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setFailMsg("motor_firmware_version", "版本号不满足要求");
      return false;
    }
    if (!addMeasureMessage(limit_app_ver, lidar_ver.app_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setFailMsg("app_firmware_version", "版本号不满足要求");
      return false;
    }
    if (!addMeasureMessage(limit_config_ver, lidar_ver.config_version, rsfsc_lib::MEASURE_DATA_TYPE_HEX))
    {
      setFailMsg("config_version", "版本号不满足要求");
      return false;
    }
    return true;
  }
  setFailMsg("require_mes_version", "获取mes版本号失败");
  return false;
}

bool AiryWaveSignalModel::initWaveCalib()
{
  if (!getLidarManager()->writeCsvData("init"))
  {
    setFailMsg(className(), "初始化失败");
    return false;
  }
  if (mount_type_ == mech::MountType::MOW)
  {
    if (!getLidarManager()->writeCsvData("init_mow"))
    {
      setFailMsg(className(), "初始化割草版寄存器失败");
      return false;
    }
  }

  if (!getLidarManager()->writeCsvCmdData("init_cmd"))
  {
    setFailMsg(className(), "初始化cmd失败");
    return false;
  }
  if (!getLidarManager()->setEyeSafe(false))
  {
    setFailMsg(className(), "关闭人眼安全失败");
    return false;
  }
  std::this_thread::sleep_for(std::chrono::seconds(2));
  if (!getLidarManager()->stopMotorToAngle(getParaInfo()->getStopMotorToAngle()))
  {
    setFailMsg(className(), "停止电机失败");
    return false;
  }
  return true;
}

bool AiryWaveSignalModel::initZeroCalib()
{
  if (!getLidarManager()->writeCsvData("init_zero"))
  {
    addMeasureMessage("fsm_collect_data", false);
    setFailMsg(className(), "采集前，初始化寄存器数据失败");
    return false;
  }
  if (mount_type_ == mech::MountType::MAPPING)
  {
    if (!getLidarManager()->writeCsvData("init_mapping_zero"))
    {
      addMeasureMessage("fsm_collect_data", false);
      setFailMsg(className(), "采集前，初始化寄存器数据失败");
      return false;
    }
  }
  else if (mount_type_ == mech::MountType::MOW)
  {
    if (!getLidarManager()->writeCsvData("init_mow"))
    {
      addMeasureMessage("fsm_collect_data", false);
      setFailMsg(className(), "采集前，初始化割草版寄存器数据失败");
      return false;
    }
  }
  return true;
}

bool AiryWaveSignalModel::checkIfChnAngleZero()
{
  std::vector<float> ver_chn_angle_vec;
  std::vector<float> hor_chn_angle_vec;

  if (!getLidarManager()->readChnAngle(ver_chn_angle_vec, hor_chn_angle_vec))
  {
    LOG_ERROR("读取通道角度失败");
    return false;
  }

  // 判断是否全零
  int allow_max_zero_num = 49;
  int zero_num           = 0;
  for (std::size_t i = 0; i < ver_chn_angle_vec.size(); ++i)
  {
    if (ver_chn_angle_vec.at(i) == 0 || hor_chn_angle_vec.at(i) == 0)
    {
      ++zero_num;
    }
  }
  if (zero_num > allow_max_zero_num)
  {
    LOG_ERROR("通道角度为0，请先写入通道角度再进行标定");
    return false;
  }

  return true;
}

bool AiryWaveSignalModel::collectPcap(const std::string& _path)
{
  TcpdumpUtils::Filter filter;
  filter.network_interface = app()->getEthName().toStdString();
  filter.protocol          = fmt::format("udp port {} or udp port {}", getLidarManager()->getLidarInfo()->getMSOPPort(),
                                         getLidarManager()->getLidarInfo()->getDIFOPPort());
  return tcpdump_utils_.startTcpdump(_path, filter);
}

bool AiryWaveSignalModel::startCollectPcap() { return collectPcap(path_.pcap_wave_file_path.toStdString()); }

bool AiryWaveSignalModel::startCollectZero(const bool _test)
{
  std::string zero_type     = _test ? "test" : "";
  auto pcap_zero_fille_path = path_.pcap_zero_file_path_unformat.toStdString();
  auto file_path            = fmt::format(pcap_zero_fille_path, zero_type);
  return collectPcap(file_path);
}

void AiryWaveSignalModel::stopCollectPcap() { tcpdump_utils_.stopTcpdump(); }

bool AiryWaveSignalModel::startDumpDifop()
{
  TcpdumpUtils::Filter filter;
  filter.network_interface = app()->getEthName().toStdString();
  filter.protocol          = fmt::format("udp port {}", getLidarManager()->getLidarInfo()->getDIFOPPort());
  return fsm_tcpdump_utils_.startTcpdump(path_.difop_file_path.toStdString(), filter);
}

void AiryWaveSignalModel::stopDumpDifop() { fsm_tcpdump_utils_.stopTcpdump(); }

void AiryWaveSignalModel::initPath(const QString& _pcap_file_path, const bool _is_zero)
{
  // 获取file_name
  QString file_name = _pcap_file_path.split("/").last();
  if (_is_zero)
  {
    if (!_pcap_file_path.isEmpty())
    {
      QDir pcap_dir(_pcap_file_path);
      path_.data_dir = pcap_dir;
      path_.data_dir.cdUp();
      path_.data_dir.cdUp();

      path_.pcap_zero_file_path = _pcap_file_path;
    }
  }
  else
  {
    auto str_list = file_name.split("_");
    LOG_INFO("{}", str_list);
    if (str_list.size() > 2)
    {
      if (str_list.at(0).contains("SN"))
      {
        QString lidar_sn = str_list.at(0).split("SN").at(1);
        getLidarManager()->getLidarInfo()->setLidarSN(lidar_sn);
        LOG_INFO("检测到雷达SN: {}", lidar_sn);
      }
      else
      {
        getLidarManager()->getLidarInfo()->setLidarSN(str_list.at(0));
      }
      LOG_INDEX_INFO("转台旋转角度{}", str_list.at(1));
    }
    if (!_pcap_file_path.isEmpty())
    {
      QDir pcap_dir(_pcap_file_path);
      path_.data_dir = pcap_dir;
      path_.data_dir.cdUp();

      path_.pcap_wave_file_path = _pcap_file_path;
    }
  }

  QDir root_dir = path_.data_dir;
  root_dir.cdUp();

  path_.result_dir       = root_dir.absolutePath() + "/result/";
  path_.detect_board_dir = path_.data_dir.absolutePath() + "/detect_board/";
  path_.dynamic_comp_dir = path_.data_dir.absolutePath() + "/dynamic/";
  path_.static_comp_dir  = path_.data_dir.absolutePath() + "/static/";
  path_.refl_comp_dir    = path_.data_dir.absolutePath() + "/refl/";
  path_.amp_comp_dir     = path_.data_dir.absolutePath() + "/amp/";
  path_.abs_comp_dir     = path_.data_dir.absolutePath() + "/abs/";
  path_.zero_dir         = path_.data_dir.absolutePath() + "/zero/";
  path_.difop_dir        = path_.data_dir.absolutePath() + "/difop/";
  path_.process_data_dir = path_.data_dir.absolutePath() + "/process_data/";

  std::vector<QDir> paths = { path_.data_dir,         path_.temp_dir,         path_.result_dir,
                              path_.detect_board_dir, path_.dynamic_comp_dir, path_.static_comp_dir,
                              path_.refl_comp_dir,    path_.amp_comp_dir,     path_.process_data_dir,
                              path_.abs_comp_dir,     path_.zero_dir,         path_.difop_dir };

  std::for_each(paths.begin(), paths.end(), [](const QDir& _dir) {
    if (!_dir.exists())
    {
      QDir().mkpath(_dir.absolutePath());
    }
  });

  if (_pcap_file_path.isEmpty())
  {
    float rotator_start                = getParaInfo()->getRotatorXAngleStart();
    float rotator_end                  = getParaInfo()->getRotatorXAngleEnd();
    auto angle                         = roundToInt(std::abs(rotator_end - rotator_start));
    QString reverse                    = rotator_start > rotator_end ? "reverse" : "regular";
    QString pcap_file_name             = QString::fromStdString(fmt::format(
                  "{}_{}_{}_{}_{}_{}.pcap", getLidarManager()->getLidarInfo()->getLidarSN(), angle,
                  QDateTime::currentDateTime().toString("yyyyMMddhhmmss"), mount_type_, reverse, app()->getFactoryLoc()));
    path_.pcap_wave_file_path          = path_.data_dir.absoluteFilePath(pcap_file_name);
    path_.pcap_zero_file_path_unformat = path_.zero_dir.absoluteFilePath(
      QString::fromStdString(fmt::format("{}_zero{}_{}.pcap", getLidarManager()->getLidarInfo()->getLidarSN(), "{}",
                                         QDateTime::currentDateTime().toString("yyyyMMddhhmmss"))));
    path_.difop_file_path = path_.difop_dir.absoluteFilePath("difop.pcap");
  }

  path_.dynamic_bit_file_name =
    QString::fromStdString(fmt::format("{}_{}.bit", getLidarManager()->getLidarInfo()->getLidarSN(), "dynamic_calib"));
  path_.static_bit_file_name =
    QString::fromStdString(fmt::format("{}_{}.bit", getLidarManager()->getLidarInfo()->getLidarSN(), "static_calib"));
  path_.refl_bit_file_name =
    QString::fromStdString(fmt::format("{}_{}.bit", getLidarManager()->getLidarInfo()->getLidarSN(), "refl_calib"));
  path_.abs_bit_file_name =
    QString::fromStdString(fmt::format("{}_{}.bit", getLidarManager()->getLidarInfo()->getLidarSN(), "abs_calib"));
  path_.combine_bit_file_name =
    QString::fromStdString(fmt::format("{}_{}.bit", getLidarManager()->getLidarInfo()->getLidarSN(), "combine_calib"));

  path_.refl90_10m_file_name = QString::fromStdString(
    fmt::format("{}_{}{}.csv", "refl90_area_array", getLidarManager()->getLidarInfo()->getLidarSN(), "{}"));

  path_.comped_eval_file_name = QString::fromStdString(
    fmt::format("{}_{}_{}.csv", "comped_eval", getLidarManager()->getLidarInfo()->getLidarSN(), "{}"));
}

bool AiryWaveSignalModel::loadPcapFile() { return loadPcapFile(path_.pcap_wave_file_path); }
bool AiryWaveSignalModel::loadPcapZero(const bool _test)
{
  std::string zero_type = _test ? "test" : "";
  auto file_path        = fmt::format(path_.pcap_zero_file_path_unformat.toStdString(), zero_type);
  return loadPcapZero(file_path.c_str(), _test);
}

bool AiryWaveSignalModel::loadPcapFile(const QString& _pcap_file_path)
{
  PcapUtils pcap_utils;
  std::string pcap_file_path = _pcap_file_path.toStdString();
  if (!pcap_utils.loadOfflinePcapFile(pcap_file_path))
  {
    LOG_ERROR("load pcap file failed: {}", pcap_file_path);
    return false;
  }
  setPcapFilePath(pcap_file_path.c_str());

  if (_pcap_file_path.endsWith("HHL.pcap"))
  {
    pcap_factory_loc_ = "HHL";
  }
  else if (_pcap_file_path.endsWith("SS.pcap"))
  {
    pcap_factory_loc_ = "SS";
  }
  else
  {
    pcap_factory_loc_ = app()->getFactoryLoc();
  }

  sample_interval_ = para_info_ptr_->getSamplingInterval();
  mount_type_      = static_cast<mech::MountType>(255);

  bool is_reverse = _pcap_file_path.contains("reverse");
  PcapUtils::PacketFilter filter;
  filter.data_length = 1248;
  PcapUtils::UdpPacketInfo udp_packet;
  std::vector<MsopPacket> msop_packets;

  LOG_INFO("采样间隔: {}", sample_interval_);

  int count  = 0;
  udp_packet = pcap_utils.getNextOfflineUdpPacket(filter);
  {
    ScopedTimer timer("get udp packet");
    while (udp_packet.udp_data != nullptr)
    {
      if ((++count) >= sample_interval_)
      {
        msop_packets.emplace_back();
        memcpy(&msop_packets.back(), udp_packet.udp_data, ntohs(udp_packet.udp_header->len) - sizeof(udphdr));
        count = 0;
      }

      udp_packet = pcap_utils.getNextOfflineUdpPacket(filter);
    }
    LOG_INFO("msop size: {}", msop_packets.size());
  }
  dist_area_map_.clear();
  temp_refl_.clear();
  float temperature = 0;
  for (int i = 1; i <= total_chn_num_; i++)
  {
    dist_area_map_[i].dist_vec.reserve(msop_packets.size() * 2);
    dist_area_map_[i].area_vec.reserve(msop_packets.size() * 2);
    dist_area_map_[i].code_mark_vec.reserve(msop_packets.size() * 2);
  }
  {
    size_t count = 1;
    ScopedTimer timer("parse dist area");
    auto process_dist_refl = [&](const auto& _dist_refl_low, const auto& _dist_refl_high, size_t _offset) {
      for (int j = 0; j < static_cast<int>(_dist_refl_low.size()); ++j)
      {
        int channel_num = static_cast<int>(_offset) + j + 1;
        auto& dist_area = dist_area_map_[channel_num];
        uint16_t area   = _dist_refl_low.at(j).refl + (_dist_refl_high.at(j).refl << 8U);
        uint16_t dist   = _dist_refl_low.at(j).dist;
        uint16_t amp    = _dist_refl_high.at(j).dist;

        dist_area.dist_vec.emplace_back(dist);
        // 面积取低15位
        dist_area.area_vec.emplace_back(area & 0x7FFFU);
        // 幅值取低12位
        dist_area.amp_vec.emplace_back(amp & 0x0FFFU);
        dist_area.code_mark_vec.emplace_back(area >> 15U);
      }
    };

    if (is_reverse)
    {
      LOG_INFO("采集数据翻转");
      std::reverse(msop_packets.begin(), msop_packets.end());
    }

    for (auto& msop_packet : msop_packets)
    {
      if (!msop_packet.isValid())
      {
        // LOG_ERROR("{} pkt_head error: 0x{:x}", count, msop_packet.pkt_head);
        auto* difop_pkt = reinterpret_cast<mech::DifopPacket*>(&msop_packet);
        if (difop_pkt->isValid())
        {
          mount_type_ = difop_pkt->mount_type;
        }
        continue;
      }
      for (size_t i = 0; i < (msop_packet.data_block.size() - 1); i += 4)
      {
        const auto& block1_low  = msop_packet.data_block.at(i);
        const auto& block1_high = msop_packet.data_block.at(i + 1);
        const auto& block2_low  = msop_packet.data_block.at(i + 2);
        const auto& block2_high = msop_packet.data_block.at(i + 3);

        process_dist_refl(block1_low.dist_refl, block2_low.dist_refl, 0);
        process_dist_refl(block1_high.dist_refl, block2_high.dist_refl, block2_high.dist_refl.size());
      }
      temperature += static_cast<float>(msop_packet.rx_temp) / 128;
      ++count;
    }
    temperature_ = static_cast<int>(std::round(temperature / static_cast<float>(count)));
    LOG_INFO("temperature: {}", temperature_);
    for (auto& [chn_num, dist_area] : dist_area_map_)
    {
      dist_area.dist_comped_vec = { dist_area.dist_vec.begin(), dist_area.dist_vec.end() };
    }
  }

  loaded_pcap_file_path_  = _pcap_file_path;
  loaded_sample_interval_ = sample_interval_;
  chn_angle_vec_.clear();
  zero_area_chn_vec_.clear();
  dynamic_len_not_enough_chn_vec_.clear();

  if (mount_type_ == mech::MountType::MOW)
  {
    total_chn_num_ = 48;
  }
  else
  {
    total_chn_num_ = 96;
  }

  if (!magic_enum::enum_contains<mech::MountType>(mount_type_))
  {
    app()->signalShowWarningText(fmt::format("警告: 识别机器的安装类型失败, 当前类型: {}", mount_type_).c_str());
    LOG_INDEX_WARN("警告: 识别机器的安装类型失败");
  }
  else
  {
    LOG_INDEX_INFO("识别到雷达安装类型: {}，当前线数：{}", mount_type_, total_chn_num_);
  }
  auto board_info_map = initBoardInfo(getDistAreaMap(1).dist_vec.size());
  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    getDistAreaMap(chn_num).board_info_map = board_info_map;
    getDistAreaMap(chn_num).dynamic_comp_result.resize(2);
    getDistAreaMap(chn_num).static_comp_result.resize(2);
    getDistAreaMap(chn_num).is_dynamic_static_comp = false;
  }
  return true;
}

bool AiryWaveSignalModel::loadPcapZero(const QString& _pcap_file_path, const bool _test)
{
  PcapUtils pcap_utils;
  if (!pcap_utils.loadOfflinePcapFile(_pcap_file_path.toStdString()))
  {
    LOG_ERROR("load pcap zero failed: {}", _pcap_file_path.toStdString());
    return false;
  }
  initPath(_pcap_file_path, true);

  PcapUtils::PacketFilter filter;
  filter.data_length = 1248;
  PcapUtils::UdpPacketInfo udp_packet;
  std::vector<MsopPacket96> msop_packets;

  udp_packet = pcap_utils.getNextOfflineUdpPacket(filter);
  while (udp_packet.udp_data != nullptr)
  {
    msop_packets.emplace_back();
    memcpy(&msop_packets.back(), udp_packet.udp_data, ntohs(udp_packet.udp_header->len) - sizeof(udphdr));

    udp_packet = pcap_utils.getNextOfflineUdpPacket(filter);
  }

  LOG_INFO("msop size: {}, dist_min: {}, dist_max: {}", msop_packets.size(), zero_dist_min_, zero_dist_max_);

  std::vector<ZeroDataPoint> data_zero;

  auto process_dist_refl = [&](const auto& _block_data) {
    auto dist_refl = _block_data.dist_refl1;
    for (int j = 0; j < static_cast<int>(dist_refl.size()); ++j)
    {
      int channel_num = j + 1;
      if (channel_num != 1)
      {
        continue;
      }
      uint16_t dist1 = dist_refl.at(j).dist;
      uint16_t area1 = dist_refl.at(j).refl;

      if (dist1 > zero_dist_max_ || dist1 < zero_dist_min_)
      {
        continue;
      }
      data_zero.emplace_back(ZeroDataPoint({ dist1, area1, _block_data.azimuth1 }));
    }
  };

  for (auto& msop_packet : msop_packets)
  {
    if (!msop_packet.isValid())
    {
      // LOG_ERROR("{} pkt_head error: 0x{:x}", count, msop_packet.pkt_head);
      auto* difop_pkt = reinterpret_cast<mech::DifopPacket*>(&msop_packet);
      if (difop_pkt->isValid())
      {
        mount_type_ = difop_pkt->mount_type;
      }
      continue;
    }
    if (msop_packet.pkt_head != MSOP_PKT_HEAD)
    {
      // LOG_ERROR("{} pkt_head error: 0x{:x}", count, msop_packet.pkt_head);
      continue;
    }

    for (size_t i = 0; i < msop_packet.data_block.size(); ++i)
    {
      const auto& block_data = msop_packet.data_block.at(i);

      process_dist_refl(block_data);
    }
  }

  if (_test)
  {
    zero_test_data_ = std::move(data_zero);
  }
  else
  {
    zero_data_ = std::move(data_zero);
  }

  return true;
}

std::map<uint16_t, ZeroAziMapData> AiryWaveSignalModel::getZeroAziMap(const std::vector<ZeroDataPoint>& _zero_data,
                                                                      const bool _is_test)
{
  zero_azi_start_ = _is_test ? getParaInfo()->getTestAziStart().toFloat() : getParaInfo()->getAziStart().toFloat();
  zero_azi_end_   = _is_test ? getParaInfo()->getTestAziEnd().toFloat() : getParaInfo()->getAziEnd().toFloat();
  std::map<uint16_t, ZeroAziMapData> azi_zero_map;
  for (const auto& [dist, area, azi] : _zero_data)
  {
    auto azi_float = static_cast<float>(azi) * 0.01F;
    if (zero_azi_start_ < 0)
    {
      if ((azi_float < (zero_azi_start_ + 360) && azi_float > zero_azi_end_))
      {
        continue;
      }
    }
    else
    {
      if (azi_float > zero_azi_end_ || azi_float < zero_azi_start_)
      {
        continue;
      }
    }

    if (azi_zero_map.find(azi) == azi_zero_map.end())
    {
      azi_zero_map[azi] = ZeroAziMapData { static_cast<double>(dist), static_cast<double>(area) };
      continue;
    }
    azi_zero_map[azi].dist_sum += dist;
    azi_zero_map[azi].area_sum += area;
    azi_zero_map[azi].count++;
  }

  for (auto& [azi, data] : azi_zero_map)
  {
    data.dist_mean = data.dist_sum / data.count;
    data.area_mean = data.area_sum / data.count;
  }

  LOG_INDEX_INFO("azi start: {}, azi end: {}, map size: {}", zero_azi_start_, zero_azi_end_, azi_zero_map.size());
  return azi_zero_map;
}
std::optional<float> AiryWaveSignalModel::getHigReflCenterAzi(const std::map<uint16_t, ZeroAziMapData>& _azi_zero_map)
{
  std::vector<double> azi_vec;
  uint16_t azi_min     = 36000;
  uint16_t azi_max     = 0;
  zero_hor_chn1_angle_ = getLidarManager()->getChnHorizontalAngle(1);
  zero_stable_error_   = getParaInfo()->getZeroStableError();

  // 遍历找到 > 150的高反点
  for (const auto& [azi, data] : _azi_zero_map)
  {
    if (data.area_mean > zero_high_refl_min_)
    {
      azi_vec.emplace_back(azi);
      azi_min = std::min(azi_min, azi);
      azi_max = std::max(azi_max, azi);
    }
  }
  if (azi_vec.empty())
  {
    LOG_INDEX_ERROR("零度角标定中未找到高反点，处理失败，高反均值阈值zero_high_refl_min_: {}", zero_high_refl_min_);
    return {};
  }
  // double azi_mean = std::accumulate(azi_vec.begin(), azi_vec.end(), 0.0) / azi_vec.size();
  double azi_max_double = azi_max;
  if (azi_max > 35000)
  {
    azi_max_double = static_cast<double>(azi_max) - 36000.;
  }
  double azi_mean = (azi_max_double + azi_min) / 2;
  azi_mean        = static_cast<float>(azi_mean) / 100. + zero_hor_chn1_angle_;
  LOG_INDEX_INFO("azi_vec: {}, azi_mean: {:.3f}, 通道角: {:.3f}, 稳定误差: {:.3f}", azi_vec, azi_mean,
                 zero_hor_chn1_angle_, zero_stable_error_);
  return azi_mean;
}
bool AiryWaveSignalModel::processZero()
{
  zero_calib_count_++;
  if (zero_data_.empty())
  {
    LOG_INDEX_ERROR("零度角标定数据为空，处理失败");
    return false;
  }

  auto azi_zero_map = getZeroAziMap(zero_data_);
  auto azi_mean     = getHigReflCenterAzi(azi_zero_map);
  if (!azi_mean)
  {
    saveZero(false);
    return false;
  }
  zero_angle_ = static_cast<float>(azi_mean.value()) + zero_stable_error_;
  if (zero_angle_ < 0)
  {
    zero_angle_ += 360;
  }
  if (zero_angle_ > 360)
  {
    zero_angle_ -= 360;
  }
  LOG_INDEX_INFO("零度角标定成功，零度角: {}, 水平角度: {}, 稳定误差: {}", zero_angle_, zero_hor_chn1_angle_,
                 zero_stable_error_);

  app()->signalUpdateZeroAngle(zero_angle_);
  return true;
}

bool AiryWaveSignalModel::processZeroTest()
{
  zero_calib_count_++;
  if (zero_test_data_.empty())
  {
    LOG_ERROR("零度角校验数据为空，处理失败");
    return false;
  }
  auto azi_zero_map = getZeroAziMap(zero_test_data_, true);
  auto azi_mean     = getHigReflCenterAzi(azi_zero_map);
  if (!azi_mean)
  {
    saveZero(true);
    return false;
  }

  zero_test_angle_ = static_cast<float>(azi_mean.value()) - zero_stable_error_;
  LOG_INDEX_INFO("零度角校验, 标定后的零度角误差: {:.3f}°, 稳定误差: {}", zero_test_angle_, zero_stable_error_);
  if (!addMeasureMessage("fsm_zero_test_error", zero_test_angle_, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    LOG_ERROR("零度角校验失败");
    saveZero(true);
    return false;
  }

  saveZero(true);
  return true;
}

bool AiryWaveSignalModel::saveDetectAllChnBoard()
{
  for (size_t i = 1; i <= total_chn_num_; ++i)
  {
    detectBoard(static_cast<int>(i));
  }
  return true;
}

bool AiryWaveSignalModel::detectBoard(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 筛选靶板", _channel_num));

  bool res                = true;
  auto total_rotate_angle = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();

  auto& dist_area_map  = getDistAreaMap(_channel_num);
  auto& board_info_map = dist_area_map.board_info_map;
  std::vector<int> dist_vec_code1_index;
  std::vector<int> dist_vec_code1;

  std::vector<int> dist_vec_code2_index;
  std::vector<int> dist_vec_code2;
  for (size_t i = 0; i < dist_area_map.dist_comped_vec.size(); ++i)
  {
    auto dist = dist_area_map.dist_comped_vec.at(i);
    if (dist_area_map.code_mark_vec.at(i) == 0)
    {
      dist_vec_code1_index.emplace_back(i);
      dist_vec_code1.emplace_back(dist);
    }
    else
    {
      dist_vec_code2_index.emplace_back(i);
      dist_vec_code2.emplace_back(dist);
    }
  }

  int dist_size   = static_cast<int>(dist_area_map.dist_vec.size());
  float chn_angle = static_cast<float>((total_chn_num_ - _channel_num) * 0.925);
  if (pcap_factory_loc_ == "HHL")
  {
    chn_angle = static_cast<float>((_channel_num - 1) * 0.9);
  }

  auto dyn_board_info       = board_info_map.at(dynamic_board_id_);
  auto detected_start_angle = static_cast<double>(dyn_board_info.detected_data_start) / dist_size * total_rotate_angle;
  auto detected_end_angle   = static_cast<double>(dyn_board_info.detected_data_end) / dist_size * total_rotate_angle;

  auto org_start_angle = dyn_board_info.board_start_angle + chn_angle;
  auto org_end_angle   = dyn_board_info.board_end_angle + chn_angle;

  size_t window_size = dist_area_map.dist_vec.size() / 1000;

  int board_20m_length =
    board_info_map[board_20m_id_].detected_data_end - board_info_map[board_20m_id_].detected_data_start;
  int board_index = board_info_map[board_20m_id_].detected_data_start - (5 * board_20m_length);
  for (auto& [board_id, board_info] : board_info_map)
  {
    if (board_info.is_found)
    {
      board_index = board_info.detected_data_end;
      continue;
    }
    if (total_rotate_angle < board_info.board_start_angle)
    {
      LOG_ERROR("board_id: {}, board_start_angle: {} 大于总角度: {}", board_id, board_info.board_start_angle,
                total_rotate_angle);
      continue;
    }
    if (total_rotate_angle < board_info.board_end_angle)
    {
      LOG_ERROR("board_id: {}, board_end_angle: {} 大于总角度: {}", board_id, board_info.board_end_angle,
                total_rotate_angle);
      continue;
    }

    if (!AnalyzeUtils::findBoardRangeByWindow(dist_area_map, board_index, window_size, board_info,
                                              !dist_area_map.is_dynamic_static_comp))
    {
      LOG_ERROR("通道{}, board_id: {}, 筛选靶板数据失败", _channel_num, board_id);
      return false;
    }
    board_index = board_info.detected_data_end;

    auto board_length = board_info.detected_data_end - board_info.detected_data_start;
    if (board_info.board_end_remove_factor > 0)
    {
      board_info.detected_data_end =
        static_cast<int>(board_info.detected_data_end - board_length * board_info.board_end_remove_factor);
    }
    if (board_info.board_start_remove_factor > 0)
    {
      board_info.detected_data_start =
        static_cast<int>(board_info.detected_data_start + board_length * board_info.board_start_remove_factor);
    }

    board_info.board_start_angle = static_cast<float>(board_info.detected_data_start) / dist_size * total_rotate_angle;
    board_info.board_end_angle   = static_cast<float>(board_info.detected_data_end) / dist_size * total_rotate_angle;

    // LOG_INFO("index start: {}, end: {}", board_info.detected_data_start, board_info.detected_data_end);
  }

  return res;
}

bool AiryWaveSignalModel::detectDynamicBoard(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 筛动标靶", _channel_num));
  auto& board_info = getDistAreaMap(_channel_num).board_info_map[dynamic_board_id_];
  auto& dist_vec   = getDistAreaMap(_channel_num).dist_vec;

  size_t window_size = getDistAreaMap(_channel_num).dist_vec.size() / 1000;
  auto org_len       = board_info.board_index_length;
  if (!AnalyzeUtils::findBoardRangeByWindow(getDistAreaMap(_channel_num), 0, window_size, board_info))
  {
    board_info.board_index_length = org_len * 0.8;
    if (AnalyzeUtils::findBoardRangeByWindow(getDistAreaMap(_channel_num), 0, window_size, board_info))
    {
      dynamic_len_not_enough_chn_vec_.emplace_back(_channel_num);
    }
    LOG_ERROR("通道{}, 筛选动标靶板数据失败", _channel_num);
    return false;
  }
  board_info.detected_data_end -= (board_info.detected_data_end - board_info.detected_data_start) * 0.05;

  if (board_info.board_end_remove_factor > 0)
  {
    board_info.detected_data_end =
      static_cast<int>(board_info.detected_data_end - ((board_info.detected_data_end - board_info.detected_data_start) *
                                                       board_info.board_end_remove_factor));
  }

  auto total_rotate_angle      = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();
  board_info.board_start_angle = static_cast<float>(board_info.detected_data_start) /
                                 getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;
  board_info.board_end_angle = static_cast<float>(board_info.detected_data_end) /
                               getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;

  return true;
}
bool AiryWaveSignalModel::detect10mBoard(const int _channel_num)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);
  auto& board_info    = dist_area_map.board_info_map[board_10m_id_];
  auto& dist_vec      = dist_area_map.dist_vec;

  size_t window_size = dist_area_map.dist_vec.size() / 1000;
  if (!AnalyzeUtils::findBoardRangeByWindow(dist_area_map, 0, window_size, board_info))
  {
    LOG_ERROR("通道{}, 筛选10m靶板数据失败", _channel_num);
    return false;
  }
  if (!detectBoardCenter(_channel_num, board_info))
  {
    return false;
  }
  auto total_rotate_angle      = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();
  board_info.board_start_angle = board_info.detected_data_start / dist_area_map.dist_vec.size() * total_rotate_angle;
  board_info.board_end_angle   = board_info.detected_data_end / dist_area_map.dist_vec.size() * total_rotate_angle;
  return true;
}
bool AiryWaveSignalModel::detect20mBoard(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 筛20米靶", _channel_num));
  auto& board_info = getDistAreaMap(_channel_num).board_info_map[board_20m_id_];
  auto& dist_vec   = getDistAreaMap(_channel_num).dist_vec;

  board_info.ignored_dist = 500;
  size_t window_size      = getDistAreaMap(_channel_num).dist_vec.size() / 1000;
  if (!AnalyzeUtils::findBoardRangeByWindow(getDistAreaMap(_channel_num), 0, window_size, board_info))
  {
    LOG_ERROR("通道{}, 筛选20m靶板数据失败", _channel_num);
    return false;
  }
  if (!detectBoardCenter(_channel_num, board_info))
  {
    return false;
  }

  auto total_rotate_angle      = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();
  board_info.board_start_angle = static_cast<float>(board_info.detected_data_start) /
                                 getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;
  board_info.board_end_angle = static_cast<float>(board_info.detected_data_end) /
                               getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;
  chn_angle_vec_.emplace_back(board_info.board_start_angle);

  return true;
}
bool AiryWaveSignalModel::detectStaticBoard(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 筛静标靶", _channel_num));
  auto& board_info = getDistAreaMap(_channel_num).board_info_map[static_board_id_];
  auto& dist_vec   = getDistAreaMap(_channel_num).dist_vec;

  size_t window_size = getDistAreaMap(_channel_num).dist_vec.size() / 1000;
  if (!AnalyzeUtils::findBoardRangeByWindow(getDistAreaMap(_channel_num), 0, window_size, board_info))
  {
    LOG_ERROR("通道{}, 筛选静标靶板数据失败", _channel_num);
    return false;
  }
  if (!detectBoardCenter(_channel_num, board_info))
  {
    return false;
  }

  auto total_rotate_angle = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();
  board_info.board_start_angle =
    board_info.detected_data_start / getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;
  board_info.board_end_angle =
    board_info.detected_data_end / getDistAreaMap(_channel_num).dist_vec.size() * total_rotate_angle;
  auto board_length = board_info.detected_data_end - board_info.detected_data_start;
  if (board_info.board_end_remove_factor > 0)
  {
    board_info.detected_data_end =
      static_cast<int>(board_info.detected_data_end - board_length * board_info.board_end_remove_factor);
  }
  if (board_info.board_start_remove_factor > 0)
  {
    board_info.detected_data_start =
      static_cast<int>(board_info.detected_data_start + board_length * board_info.board_start_remove_factor);
  }
  return true;
}

bool AiryWaveSignalModel::detectReflBoardCenter(const int _channel_num)
{
  auto& dist_area_map  = getDistAreaMap(_channel_num);
  auto& board_info_map = getDistAreaMap(_channel_num).board_info_map;
  auto& dist_vec       = dist_area_map.dist_vec;
  auto& area_vec       = dist_area_map.area_vec;
  auto& amp_vec        = dist_area_map.amp_vec;
  auto& code_mark_vec  = dist_area_map.code_mark_vec;

  for (auto board_id : refl_board_id_vec_)
  {
    auto& board_info = board_info_map[board_id];
    if (!detectBoardCenter(_channel_num, board_info))
    {
      return false;
    }
  }

  // 动标弱通道判断
  if ((_channel_num % 4) == 0)
  {
    int min_area_channel_num = _channel_num - 3;
    auto refl90_board        = getDistAreaMap(min_area_channel_num).board_info_map[board_10m_id_].getReflBoard(90);
    auto refl90_10m          = refl90_board.area_mean;
    for (int i = min_area_channel_num + 1; i <= _channel_num; ++i)
    {
      auto curr_refl90_board = getDistAreaMap(i).board_info_map[board_10m_id_].getReflBoard(90);
      if (curr_refl90_board.area_mean < refl90_10m)
      {
        refl90_10m           = curr_refl90_board.area_mean;
        min_area_channel_num = i;
      }
    }
    int dynamic_chn_num_common = _channel_num - 2;
    for (int i = _channel_num - 3; i <= _channel_num; ++i)
    {
      getDistAreaMap(i).refl90_is_min_area = (i == min_area_channel_num);
    }
  }
  return true;
}

bool AiryWaveSignalModel::detectBoardCenter(const int _channel_num, BoardInfo& _board_info, const int _window_size)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);
  // auto& dist_vec      = dist_area_map.dist_vec;
  auto& dist_comped_vec = dist_area_map.dist_comped_vec;
  auto& area_vec        = dist_area_map.area_vec;
  auto& amp_vec         = dist_area_map.amp_vec;
  auto& code_mark_vec   = dist_area_map.code_mark_vec;
  auto& raw_dist_vec    = dist_area_map.dist_vec;
  if (!_board_info.is_found)
  {
    LOG_ERROR("refl target_id: {} not found", _board_info.board_id);
    return false;
  }
  size_t start = _board_info.detected_data_start;
  size_t end   = _board_info.detected_data_end;
  for (auto& board_pos : _board_info.refl_board_vec)
  {
    auto refl         = board_pos.refl;
    auto center_index = std::lround(static_cast<double>(start) + (board_pos.pos_factor * (end - start)));

    // size_t area_mid_value_index =
    // AnalyzeUtils::getMidValueIndexWithSameCodeMark(area_vec, code_mark_vec, center_index, 200);
    // size_t amp_mid_value_index =
    //   AnalyzeUtils::getMidValueIndexWithSameCodeMark(amp_vec, code_mark_vec, center_index, 200);

    std::vector<std::vector<float>> area_code_mark(2);
    std::vector<std::vector<float>> dist_code_mark(2);
    std::vector<std::vector<float>> amp_code_mark(2);

    if (center_index < _window_size || (center_index + _window_size) > dist_comped_vec.size())
    {
      LOG_ERROR("board {} 筛选范围出现错误，无法提取到中心点", _board_info.board_id);
      return false;
    }

    for (size_t i = center_index - _window_size; i < center_index + _window_size; ++i)
    {
      auto& dist     = dist_comped_vec.at(i);
      auto& area     = area_vec.at(i);
      auto& amp      = amp_vec.at(i);
      auto& code     = code_mark_vec.at(i);
      auto& raw_dist = raw_dist_vec.at(i);

      if (dist_area_map.is_dynamic_static_comp)
      {
        if (dist < (_board_info.comped_dist_min - 200) || (dist > _board_info.comped_dist_max + 200))
        {
          continue;
        }
      }
      else
      {
        if ((dist < _board_info.raw_dist_min) || (dist > _board_info.raw_dist_max))
        {
          continue;
        }
      }
      if (area < 10)
      {
        continue;
      }

      area_code_mark.at(code).emplace_back(area);
      amp_code_mark.at(code).emplace_back(amp);
      dist_code_mark.at(code).emplace_back(dist);
    }

    size_t area_mid_value_index = center_index;
    size_t amp_mid_value_index  = center_index;

    auto is_code_0_empty = area_code_mark.at(0).empty();
    auto is_code_1_empty = area_code_mark.at(1).empty();

    if (is_code_0_empty)
    {
      LOG_INDEX_ERROR("通道 {} ,{:.0f}m {}反射率靶板中心code{}数据为空", _channel_num, _board_info.distance / 1000.0F,
                      refl, 0);
    }
    if (is_code_1_empty)
    {
      LOG_INDEX_ERROR("通道 {} ,{:.0f}m {}反射率靶板中心code{}数据为空", _channel_num, _board_info.distance / 1000.0F,
                      refl, 1);
    }

    if (is_code_0_empty && is_code_1_empty)
    {
      LOG_INDEX_ERROR("通道 {} ,{}筛选中心数据empty，提取{}反射率靶板中心失败", _channel_num, _board_info.board_id,
                      refl);
      return false;
    }

    float true_value = _board_info.distance_05cm;
    if (board_pos.distance_05cm > 0)
    {
      true_value = board_pos.distance_05cm;
    }

    auto area_mean_code_0    = AnalyzeUtils::getMeanValue(area_code_mark.at(0));
    auto amp_mean_code_0     = AnalyzeUtils::getMeanValue(amp_code_mark.at(0));
    auto dist_static_param_0 = AnalyzeUtils::getStatisticParam(dist_code_mark.at(0), true_value);
    // auto area_static_param_0 = AnalyzeUtils::getStatisticParam(area_code_mark.at(0), true_value);

    auto area_mean_code_1    = AnalyzeUtils::getMeanValue(area_code_mark.at(1));
    auto amp_mean_code_1     = AnalyzeUtils::getMeanValue(amp_code_mark.at(1));
    auto dist_static_param_1 = AnalyzeUtils::getStatisticParam(dist_code_mark.at(1), true_value);
    // auto area_static_param_1 = AnalyzeUtils::getStatisticParam(area_code_mark.at(1), true_value);

    // 如果其中一个code为0,则使用另一个code的值
    if (area_mean_code_0 <= 0.1)
    {
      area_mean_code_0 = area_mean_code_1;
    }
    if (area_mean_code_1 <= 0.1)
    {
      area_mean_code_1 = area_mean_code_0;
    }
    if (amp_mean_code_0 <= 0.1)
    {
      amp_mean_code_0 = amp_mean_code_1;
    }
    if (amp_mean_code_1 <= 0.1)
    {
      amp_mean_code_1 = amp_mean_code_0;
    }
    if (!dist_static_param_0.has_value())
    {
      dist_static_param_0 = dist_static_param_1;
    }
    if (!dist_static_param_1.has_value())
    {
      dist_static_param_1 = dist_static_param_0;
    }
    if (!dist_static_param_0.has_value() && !dist_static_param_1.has_value())
    {
      LOG_INDEX_ERROR("通道 {} ,{}筛选中心数据，提取{}反射率靶板中心失败", _channel_num, _board_info.board_id, refl);
      return false;
    }

    board_pos.area_mean  = (area_mean_code_1 + area_mean_code_0) / 2;
    board_pos.amp_mean   = (amp_mean_code_1 + amp_mean_code_0) / 2;
    board_pos.dist_mean  = (dist_static_param_0->mean + dist_static_param_1->mean) / 2;
    board_pos.dist_range = (dist_static_param_0->range + dist_static_param_1->range) / 2;
    board_pos.dist_err_sd =
      (dist_static_param_0->error_standard_deviation + dist_static_param_1->error_standard_deviation) / 2;

    board_pos.dist_mean_code_0 = dist_static_param_0->mean;
    board_pos.dist_mean_code_1 = dist_static_param_1->mean;
    board_pos.area_mean_code_0 = area_mean_code_0;
    board_pos.area_mean_code_1 = area_mean_code_1;
    board_pos.amp_mean_code_0  = amp_mean_code_0;
    board_pos.amp_mean_code_1  = amp_mean_code_1;
    board_pos.area_index       = static_cast<int>(center_index);
    board_pos.amp_index        = static_cast<int>(center_index);
  }
  return true;
}

bool AiryWaveSignalModel::fix10mBoard(const int _channel_num)
{

  if (pcap_factory_loc_ == "HHL" || app()->getFactoryLoc() == "HHL")
  {
    return true;
  }
  if (_channel_num < 91)
  {
    return true;
  }
  auto fix_board_id = board_10m_id_ + 1;
  if (getDistAreaMap(_channel_num).board_info_map.find(fix_board_id) ==
      getDistAreaMap(_channel_num).board_info_map.end())
  {
    LOG_ERROR("fix_board_id not found {}", fix_board_id);
    return false;
  }
  auto& board_info = getDistAreaMap(_channel_num).board_info_map[fix_board_id];

  float len_factor = board_info.detected_data_end - board_info.detected_data_start;
  getDistAreaMap(_channel_num).board_info_map[board_10m_id_].detected_data_end =
    board_info.detected_data_start - (len_factor)*0.2;
  return true;
}

bool AiryWaveSignalModel::detectBoardUseTrueDist(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 筛选真值靶板", _channel_num));

  auto& dist_area_map = getDistAreaMap(_channel_num);
  int window_size     = static_cast<int>(dist_area_map.dist_vec.size() / 1000);

  int curr_index = 0;
  for (auto& [board_id, board_info] : dist_area_map.board_info_map)
  {
    if (!AnalyzeUtils::findBoardRangeByWindow(dist_area_map, curr_index, window_size, board_info,
                                              !dist_area_map.is_dynamic_static_comp))
    {
      LOG_ERROR("测距数据检查异常，通道: {}, 靶板: {}", _channel_num, board_id);
      return false;
    }
    curr_index = board_info.detected_data_end;
    if (!detectBoardCenter(_channel_num, board_info, window_size))
    {
      LOG_ERROR("测距数据检查异常，通道: {}, 靶板: {}", _channel_num, board_id);
      return false;
    }
  }
  return true;
}

bool AiryWaveSignalModel::fixBoardCompedDist(const int _channel_num)
{
  auto& dist_area_map  = getDistAreaMap(_channel_num);
  auto& board_info_map = dist_area_map.board_info_map;
  for (auto& [board_id, board_info] : board_info_map)
  {
    float refl_board_distance_min = board_info.distance_05cm;
    float refl_board_distance_max = board_info.distance_05cm;
    for (auto& refl_board : board_info.refl_board_vec)
    {
      if (refl_board.distance_05cm > 0 && refl_board.distance_05cm < refl_board_distance_min)
      {
        refl_board_distance_min = refl_board.distance_05cm;
      }
      if (refl_board.distance_05cm > 0 && refl_board.distance_05cm > refl_board_distance_max)
      {
        refl_board_distance_max = refl_board.distance_05cm;
      }
    }
    if (board_info.comped_dist_min < 0)
    {
      board_info.comped_dist_min = refl_board_distance_min - 10;
    }
    if (board_info.comped_dist_max < 0)
    {
      board_info.comped_dist_max = refl_board_distance_max + 10;
      if (board_id == board_20m_id_)
      {
        board_info.comped_dist_max += 60;
      }
      if (board_id == board_3m_id_)
      {
        board_info.comped_dist_max += 10;
      }
    }
  }
  return true;
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
std::vector<float> AiryWaveSignalModel::fitDynamic(const int _channel_num,
                                                   std::vector<float>& _area,
                                                   std::vector<float>& _dist,
                                                   std::vector<float>& _area_20m,
                                                   std::vector<float>& _dist_20m)
{
  // 根据拟合的面积-距离，面积最小值为area_min，小于该面积的距离为dist_min都设置为拟合曲线area_min所对应的dist数值
  // 不在area范围内的直接拉平
  BSplineFit bspline_fit;
  bspline_fit.regularizedEndpointBsplineFit(_area, _dist);
  auto area_left  = *std::min_element(_area.begin(), _area.end());
  auto area_right = *std::max_element(_area.begin(), _area.end());
  auto dist_left  = bspline_fit.getY(area_left);
  auto dist_right = bspline_fit.getY(area_right);

  auto& dist_area_map = getDistAreaMap(_channel_num);

  std::vector<float> fit_result(32768);
  for (int i = 0; i < 20; ++i)
  {
    _area_20m.emplace_back(area_left + 150);
    _dist_20m.emplace_back(bspline_fit.getY(area_left + 150));
  }
  auto max_area_20m  = *std::max_element(_area_20m.begin(), _area_20m.end());
  auto mean_area_20m = AnalyzeUtils::getMeanValue(_area_20m, _area_20m.size() / 2, _area_20m.size() / 2);
  PolyFit poly_fit;
  poly_fit.linearFit(_area_20m, _dist_20m);

  float seg_area    = 4000;
  auto area_seg_min = std::min(seg_area, area_left);
  float slope_left  = 12.0 / 1000;
  float slope_right = 6.0 / 1000;

  if (mean_area_20m > 4000)
  {
    for (int i = 0; i < fit_result.size(); ++i)
    {
      float area = static_cast<float>(i);
      if (area < max_area_20m)
      {
        fit_result.at(i) = poly_fit.getY(area);
      }
      else if (area < seg_area)
      {
        if (area_left >= seg_area)
        {
          float comp       = static_cast<float>(seg_area - area) * slope_left;
          fit_result.at(i) = static_cast<float>(dist_left + (area_left - seg_area) * slope_right + comp);
        }
        else
        {
          fit_result.at(i) = static_cast<float>(bspline_fit.getY(seg_area) + (seg_area - area) * slope_left);
        }
      }
      else if (area >= seg_area && area < area_left)
      {
        fit_result.at(i) = static_cast<float>(dist_left + (area_left - area) * slope_right);
      }
      else if (area >= area_right)  // 超过右边界
      {
        fit_result.at(i) = static_cast<float>(dist_right);
      }
      else  // 使用 B 样条拟合
      {
        fit_result.at(i) = static_cast<float>(bspline_fit.getY(area));
      }
    }
    return fit_result;
  }

  float transition_width = 50;  // 可配置的过渡宽度
  float start_transition = max_area_20m - transition_width;
  float end_transition   = max_area_20m + transition_width;

  for (int i = 0; i < fit_result.size(); ++i)
  {
    float area = static_cast<float>(i);
    if (area < start_transition)
    {
      // 使用多项式拟合
      fit_result.at(i) = poly_fit.getY(area);
    }
    else if (area >= end_transition)
    {
      // 如果超过了B样条的使用范围，则使用dist_right
      if (area >= area_right)
      {
        fit_result.at(i) = static_cast<float>(dist_right);
      }
      else
      {
        // 使用B样条拟合
        fit_result.at(i) = static_cast<float>(bspline_fit.getY(area));
      }
    }
    else
    {
      // 在过渡区域内使用加权平均进行平滑过渡
      float weight     = (area - start_transition) / (end_transition - start_transition);
      fit_result.at(i) = static_cast<float>((1 - weight) * poly_fit.getY(area) + weight * bspline_fit.getY(area));
    }
  }

  return fit_result;
}
bool AiryWaveSignalModel::extractReflCenterMeanData(const int _channel_num,
                                                    const int _board_id,
                                                    const int _refl,
                                                    std::vector<std::vector<float>>& _dist_vec,
                                                    std::vector<std::vector<float>>& _area_vec)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);

  // 获取指定板块和反射率的中心索引和距离
  auto center_index  = dist_area_map.getReflBoard(_board_id, _refl).area_index;
  auto distance_05cm = dist_area_map.getReflBoard(_board_id, _refl).distance_05cm;

  if (!dist_area_map.board_info_map[_board_id].is_found)
  {
    LOG_ERROR("未找到靶板 board_id: {} not found", _board_id);
    return false;
  }

  int window_size = 50;
  for (size_t i = center_index - window_size; i < center_index + window_size; ++i)
  {
    int area   = dist_area_map.area_vec.at(i);
    float dist = static_cast<float>(dist_area_map.dist_vec.at(i));

    // 检查距离是否在有效范围内
    if (dist > dist_area_map.board_info_map[_board_id].raw_dist_max)
    {
      continue;
    }
    if (dist < dist_area_map.board_info_map[_board_id].raw_dist_min)
    {
      continue;
    }

    // 距离校正
    dist -= distance_05cm;
    if (dist < 0)
    {
      continue;
    }

    // 检查面积是否在有效范围内
    auto code = dist_area_map.code_mark_vec.at(i);
    if (area < dynamic_area_min_)
    {
      continue;
    }
    if (area > dynamic_area_max_)
    {
      continue;
    }

    // 添加有效数据点
    _dist_vec.at(code).emplace_back(dist);
    _area_vec.at(code).emplace_back(area);
  }

  // 处理提取的数据
  for (int i = 0; i < _dist_vec.size(); ++i)
  {
    // 去除异常值
    AnalyzeUtils::eraseOutlier(_area_vec.at(i), _dist_vec.at(i), 3, 0.7);

    // 计算均值
    auto dist_mean = std::accumulate(_dist_vec.at(i).begin(), _dist_vec.at(i).end(), 0.0) /
                     static_cast<double>(_dist_vec.at(i).size());
    auto area_mean = std::accumulate(_area_vec.at(i).begin(), _area_vec.at(i).end(), 0.0) /
                     static_cast<double>(_area_vec.at(i).size());

    // 清空并填充均值数据
    _dist_vec.at(i).clear();
    _area_vec.at(i).clear();
    for (int j = 0; j < 20; ++j)
    {
      _dist_vec.at(i).emplace_back(dist_mean);
      _area_vec.at(i).emplace_back(area_mean);
    }
  }
  return true;
}

bool AiryWaveSignalModel::extractReflCenterData(const int _channel_num,
                                                const int _board_id,
                                                const int _refl,
                                                std::vector<std::vector<float>>& _dist_vec,
                                                std::vector<std::vector<float>>& _area_vec)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);

  // 获取指定板块和反射率的中心索引和距离
  auto center_index  = dist_area_map.getReflBoard(_board_id, _refl).area_index;
  auto distance_05cm = dist_area_map.getReflBoard(_board_id, _refl).distance_05cm;

  if (!dist_area_map.board_info_map[_board_id].is_found)
  {
    LOG_ERROR("未找到靶板 board_id: {} not found", _board_id);
    return false;
  }

  int window_size = 100;
  for (size_t i = center_index - window_size; i < center_index + window_size; ++i)
  {
    int area   = dist_area_map.area_vec.at(i);
    float dist = static_cast<float>(dist_area_map.dist_vec.at(i));

    // 检查距离是否在有效范围内
    if (dist > dist_area_map.board_info_map[_board_id].raw_dist_max)
    {
      continue;
    }
    if (dist < dist_area_map.board_info_map[_board_id].raw_dist_min)
    {
      continue;
    }

    // 距离校正
    dist -= distance_05cm;
    if (dist <= 0)
    {
      continue;
    }

    // 检查面积是否在有效范围内
    auto code = dist_area_map.code_mark_vec.at(i);
    if (area < dynamic_area_min_)
    {
      continue;
    }
    if (area > dynamic_area_max_)
    {
      continue;
    }

    // 添加有效数据点
    _dist_vec.at(code).emplace_back(dist);
    _area_vec.at(code).emplace_back(area);
  }

  // 处理提取的数据
  for (int i = 0; i < _dist_vec.size(); ++i)
  {
    // 去除异常值
    AnalyzeUtils::eraseOutlier(_area_vec.at(i), _dist_vec.at(i), 3, 0.7);

    // 计算均值
    // auto dist_mean = std::accumulate(_dist_vec.at(i).begin(), _dist_vec.at(i).end(), 0.0) /
    //                  static_cast<double>(_dist_vec.at(i).size());
    // auto area_mean = std::accumulate(_area_vec.at(i).begin(), _area_vec.at(i).end(), 0.0) /
    //                  static_cast<double>(_area_vec.at(i).size());

    // // 清空并填充均值数据
    // _dist_vec.at(i).clear();
    // _area_vec.at(i).clear();
    // for (int j = 0; j < 20; ++j)
    // {
    //   _dist_vec.at(i).emplace_back(dist_mean);
    //   _area_vec.at(i).emplace_back(area_mean);
    // }
  }
  return true;
}

bool AiryWaveSignalModel::getDynamicTargetBoardData(const int _channel_num,
                                                    const BoardInfo& _board_info,
                                                    const double _angle_per_sample,
                                                    std::vector<std::vector<float>>& _dyn_dist_vec,
                                                    std::vector<std::vector<float>>& _dyn_area_vec,
                                                    int& _max_area,
                                                    int& _dynamic_start_index)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);
  size_t start        = _board_info.detected_data_start;
  size_t end          = _board_info.detected_data_end;
  end                 = end - (end - start) * dynamic_remove_low_refl_factor_;

  // 计算中心角度
  double center_angle = (static_cast<double>(end - start) / 2 + static_cast<double>(start)) * _angle_per_sample;
  float distance_05cm = _board_info.distance_05cm;

  // 初始化最大面积
  _max_area = 0;

  // 遍历靶板数据范围
  for (size_t i = start; i < end; ++i)
  {
    int area = dist_area_map.area_vec.at(i);

    // 检查相邻点的面积差异
    int prev_area      = dist_area_map.area_vec.at(i - 1);
    int next_area      = dist_area_map.area_vec.at(i + 1);
    int area_diff      = std::abs(area - prev_area);
    int next_area_diff = std::abs(area - next_area);

    // 检查相邻点的距离差异
    float dist      = static_cast<float>(dist_area_map.dist_vec.at(i));
    float prev_dist = static_cast<float>(dist_area_map.dist_vec.at(i - 1));
    float next_dist = static_cast<float>(dist_area_map.dist_vec.at(i + 1));
    float dist_diff = std::abs(dist - prev_dist);
    float next_diff = std::abs(dist - next_dist);

    // 过滤异常数据点
    if (area_diff > 500 && next_area_diff > 500)
    {
      continue;
    }

    if (dist_diff > 300 && next_diff > 300)
    {
      continue;
    }

    if (dist > (_board_info.raw_dist_max))
    {
      continue;
    }
    if (dist < (_board_info.raw_dist_min))
    {
      continue;
    }

    // 角度补偿
    float curr_angle = static_cast<float>(i) * _angle_per_sample;
    // 由于distance是中心的距离，需要根据临边是中心距离，和角度求出斜边距离
    float curr_dist = distance_05cm;
    if (getParaInfo()->getDynAngleComp())
    {
      curr_dist /= std::cos((curr_angle - center_angle) * M_PI / 180);
    }
    dist -= curr_dist;

    // 获取编码标记
    auto code = dist_area_map.code_mark_vec.at(i);

    // 检查面积是否在有效范围内
    if (area < dynamic_area_min_)
    {
      continue;
    }
    if (area > dynamic_area_max_)
    {
      continue;
    }

    // 如果找到更大的面积，更新最大面积和起始索引，并清空之前的数据
    if (area >= _max_area)
    {
      _max_area            = area;
      _dynamic_start_index = static_cast<int>(i);
      for (int j = 0; j < _dyn_area_vec.size(); ++j)
      {
        _dyn_dist_vec.at(j).clear();
        _dyn_area_vec.at(j).clear();
      }
    }

    // 添加有效数据点
    _dyn_dist_vec.at(code).emplace_back(dist);
    _dyn_area_vec.at(code).emplace_back(area);
  }

  return true;
}

bool AiryWaveSignalModel::checkDynConditions(const int _channel_num, BoardInfo& _board_info)
{
  auto& dist_area_map = getDistAreaMap(_channel_num);

  // 检查靶板是否找到
  if (!_board_info.is_found)
  {
    LOG_ERROR("未找到动标靶板 target_id: {} not found", dynamic_board_id_);
    return false;
  }

  // 获取靶板数据范围
  auto start        = _board_info.detected_data_start;
  auto end          = _board_info.detected_data_end;
  auto total_length = dist_area_map.dist_vec.size();

  // 检查limit文件
  auto dynamic_20m_ref_dist = limit_csv_utils_ptr_->getLimitInfo("fsm_dynamic_left_dist");
  if (!dynamic_20m_ref_dist.is_ok)
  {
    LOG_ERROR("limit文件获取失败");
    return false;
  }

  // 检查数据范围是否有效
  if (start < 1 || end > total_length - 2)
  {
    LOG_ERROR("动标数据靶板检测异常，start: {}, end: {}, total_length: {}", start, end, total_length);
    return false;
  }

  return true;
}

bool AiryWaveSignalModel::preprocessAndCheckDyn(const int /* _channel_num */,
                                                std::vector<std::vector<float>>& _dyn_dist_vec,
                                                std::vector<std::vector<float>>& _dyn_area_vec,
                                                std::vector<std::vector<float>>& _dyn_dist_vec_20m,
                                                std::vector<std::vector<float>>& _dyn_area_vec_20m)
{
  // 检查数据有效性
  for (int i = 0; i < _dyn_dist_vec.size(); ++i)
  {
    if (_dyn_dist_vec.at(i).size() < 50)
    {
      LOG_ERROR("动标编码数据缺失，小于50 code: {}, size: {}", 1 - i, _dyn_dist_vec.at(i).size());
      return false;
    }
  }

  // 预处理数据：排序和去除异常值
  for (int i = 0; i < _dyn_dist_vec.size(); ++i)
  {
    // 对动标数据进行排序和去除异常值
    AnalyzeUtils::sortVectors(_dyn_area_vec.at(i), _dyn_dist_vec.at(i));
    AnalyzeUtils::eraseOutlier(_dyn_area_vec.at(i), _dyn_dist_vec.at(i), 5, 0.5);

    // 对20m反射率数据进行处理（如果有）
    if (!_dyn_dist_vec_20m.empty() && !_dyn_area_vec_20m.empty())
    {
      if (i < _dyn_dist_vec_20m.size() && i < _dyn_area_vec_20m.size())
      {
        if (!_dyn_dist_vec_20m.at(i).empty() && !_dyn_area_vec_20m.at(i).empty())
        {
          AnalyzeUtils::sortVectors(_dyn_area_vec_20m.at(i), _dyn_dist_vec_20m.at(i));
          AnalyzeUtils::eraseOutlier(_dyn_area_vec_20m.at(i), _dyn_dist_vec_20m.at(i), 3, 0.7);
        }
      }
    }
  }

  return true;
}

bool AiryWaveSignalModel::processDynamicData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 动标数据", _channel_num));
  auto& dist_area_map       = getDistAreaMap(_channel_num);
  auto& board_info_map      = dist_area_map.board_info_map;
  auto& board_info          = board_info_map[dynamic_board_id_];
  auto& code_mark           = dist_area_map.dynamic_code_mark;
  auto& dynamic_start_index = dist_area_map.dynamic_start_index;
  dist_area_map.dynamic_comp_result.resize(2);
  bool is_success     = true;
  dynamic_start_index = board_info_map[dynamic_board_id_].detected_data_start;
  code_mark           = (_channel_num % 2) == 0 ? 0 : 1;

  // 检查动标处理条件
  auto start        = board_info.detected_data_start;
  auto end          = board_info.detected_data_end;
  auto total_length = dist_area_map.dist_vec.size();
  // 计算角度参数
  double total_angle    = getParaInfo()->getRotatorXAngleEnd() - getParaInfo()->getRotatorXAngleStart();
  total_angle           = std::abs(total_angle);
  auto angle_per_sample = total_angle / static_cast<double>(total_length);
  if (!checkDynConditions(_channel_num, board_info))
  {
    return false;
  }

  // 动标曲线范围，找到最大的面积，从面积最大值开始
  int max_area = 0;
  std::vector<std::vector<float>> dyn_dist_vec(2);
  std::vector<std::vector<float>> dyn_area_vec(2);
  // 获取动标靶板数据
  getDynamicTargetBoardData(_channel_num, board_info, angle_per_sample, dyn_dist_vec, dyn_area_vec, max_area,
                            dynamic_start_index);

  // 提取20m, 10反射率数据
  std::vector<std::vector<float>> dyn_dist_vec_20m_10refl(2);
  std::vector<std::vector<float>> dyn_area_vec_20m_10refl(2);
  std::vector<std::vector<float>> dyn_dist_vec_20m_40refl(2);
  std::vector<std::vector<float>> dyn_area_vec_20m_40refl(2);
  std::vector<std::vector<float>> dyn_dist_vec_20m_90refl(2);
  std::vector<std::vector<float>> dyn_area_vec_20m_90refl(2);

  std::vector<std::vector<float>> dyn_dist_vec_10m_10refl(2);
  std::vector<std::vector<float>> dyn_area_vec_10m_10refl(2);
  std::vector<std::vector<float>> dyn_dist_vec_10m_40refl(2);
  std::vector<std::vector<float>> dyn_area_vec_10m_40refl(2);
  std::vector<std::vector<float>> dyn_dist_vec_10m_90refl(2);
  std::vector<std::vector<float>> dyn_area_vec_10m_90refl(2);

  if (!extractReflCenterData(_channel_num, board_20m_id_, 10, dyn_dist_vec_20m_10refl, dyn_area_vec_20m_10refl))
  {
    return false;
  }
  if (!extractReflCenterData(_channel_num, board_20m_id_, 40, dyn_dist_vec_20m_40refl, dyn_area_vec_20m_40refl))
  {
    return false;
  }
  if (!extractReflCenterData(_channel_num, board_20m_id_, 90, dyn_dist_vec_20m_90refl, dyn_area_vec_20m_90refl))
  {
    return false;
  }
  if (!extractReflCenterData(_channel_num, board_10m_id_, 10, dyn_dist_vec_10m_10refl, dyn_area_vec_10m_10refl))
  {
    return false;
  }
  if (!extractReflCenterData(_channel_num, board_10m_id_, 40, dyn_dist_vec_10m_40refl, dyn_area_vec_10m_40refl))
  {
    return false;
  }
  if (!extractReflCenterData(_channel_num, board_10m_id_, 90, dyn_dist_vec_10m_90refl, dyn_area_vec_10m_90refl))
  {
    return false;
  }

  // 将20m_10refl的数据和20m_40refl的数据添加到dyn_dist_vec和dyn_area_vec中
  for (int i = 0; i < dyn_dist_vec_20m_10refl.size(); ++i)
  {
    // dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_20m_10refl.at(i).begin(),
    //                           dyn_dist_vec_20m_10refl.at(i).end());
    // dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_20m_10refl.at(i).begin(),
    //                           dyn_area_vec_20m_10refl.at(i).end());
    dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_20m_40refl.at(i).begin(),
                              dyn_dist_vec_20m_40refl.at(i).end());
    dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_20m_40refl.at(i).begin(),
                              dyn_area_vec_20m_40refl.at(i).end());
    dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_20m_90refl.at(i).begin(),
                              dyn_dist_vec_20m_90refl.at(i).end());
    dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_20m_90refl.at(i).begin(),
                              dyn_area_vec_20m_90refl.at(i).end());
    dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_10m_10refl.at(i).begin(),
                              dyn_dist_vec_10m_10refl.at(i).end());
    dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_10m_10refl.at(i).begin(),
                              dyn_area_vec_10m_10refl.at(i).end());
    dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_10m_40refl.at(i).begin(),
                              dyn_dist_vec_10m_40refl.at(i).end());
    dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_10m_40refl.at(i).begin(),
                              dyn_area_vec_10m_40refl.at(i).end());
    dyn_dist_vec.at(i).insert(dyn_dist_vec.at(i).end(), dyn_dist_vec_10m_90refl.at(i).begin(),
                              dyn_dist_vec_10m_90refl.at(i).end());
    dyn_area_vec.at(i).insert(dyn_area_vec.at(i).end(), dyn_area_vec_10m_90refl.at(i).begin(),
                              dyn_area_vec_10m_90refl.at(i).end());
  }

  // 检查数据有效性并预处理
  if (!preprocessAndCheckDyn(_channel_num, dyn_dist_vec, dyn_area_vec, dyn_dist_vec_20m_10refl,
                             dyn_area_vec_20m_10refl))
  {
    return false;
  }

  int index = 0;
  for (int i = 0; i < dyn_dist_vec.size(); ++i)
  {
    try
    {
      dist_area_map.dynamic_comp_result.at(i) =
        fitDynamic(_channel_num, dyn_area_vec.at(i), dyn_dist_vec.at(i), dyn_area_vec_20m_10refl.at(i),
                   dyn_dist_vec_20m_10refl.at(i));
    }
    catch (const std::runtime_error& e)
    {
      LOG_ERROR("通道: {}，拟合数据异常，无法处理此数据: {}，当前采样间隔：{}，请调整采样间隔或联系开发者处理",
                e.what(), _channel_num, sample_interval_);
      return false;
    }
    auto min_dist =
      *std::min_element(dist_area_map.dynamic_comp_result.at(i).begin(), dist_area_map.dynamic_comp_result.at(i).end());
    for (auto& dist : dist_area_map.dynamic_comp_result.at(i))
    {
      if (dist >= min_dist)
      {
        dist -= min_dist;
      }
    }
    if (code_mark == i)
    {
      dist_area_map.dynamic_min_dist = min_dist;
    }
    auto dist_left = dist_area_map.dynamic_comp_result.at(i).at(0);
    if (!limit_csv_utils_ptr_->checkWithinLimit("fsm_dynamic_left_dist", dist_left))
    {
      LOG_ERROR("通道: {}，动标低反距离数据超出限制，当前距离：{}", _channel_num, dist_left);
      addMeasureMessage("fsm_dynamic_left_dist", dist_left, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
      is_success = false;
    }
  }

  // 补偿动标
  for (size_t i = 0; i < dist_area_map.dist_comped_vec.size(); ++i)
  {
    auto dist      = dist_area_map.dist_comped_vec.at(i);
    auto area      = dist_area_map.area_vec.at(i);
    auto code_mark = dist_area_map.code_mark_vec.at(i);
    auto dyn_comp  = dist_area_map.dynamic_comp_result.at(code_mark).at(area);
    // auto dyn_comp = dynamic_comp_fit_result.at(area);
    if (dist >= dyn_comp)
    {
      dist_area_map.dist_comped_vec.at(i) -= dyn_comp;
    }
  }

  return is_success;
}

bool AiryWaveSignalModel::processStaticData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 静标数据", _channel_num));
  auto& dist_area_map   = getDistAreaMap(_channel_num);
  auto& dist_comped_vec = dist_area_map.dist_comped_vec;
  auto& board_info_map  = dist_area_map.board_info_map;
  auto& board_info      = board_info_map[static_board_id_];

  if (!board_info.is_found)
  {
    LOG_ERROR("static target_id: {} not found", board_info.board_id);
    return false;
  }
  size_t start = board_info.detected_data_start;
  size_t end   = board_info.detected_data_end;

  if ((end - start) < 20)
  {
    LOG_ERROR("静标编码数据缺失，小于20 code: {}, size: {}", 1, (end - start));
    return false;
  }

  if (dist_area_map.dynamic_comp_result.at(dist_area_map.dynamic_code_mark).empty())
  {
    LOG_ERROR("dynamic comp result is empty, channel num: {}", _channel_num);
    return false;
  }

  std::vector<std::vector<float>> static_dist_vec(2);
  std::vector<std::vector<float>> static_area_vec(2);
  dist_area_map.static_comp_result.resize(2);

  for (size_t i = start; i < end; ++i)
  {
    auto code_mark = dist_area_map.code_mark_vec.at(i);
    if (dist_comped_vec.at(i) > board_info.raw_dist_max)
    {
      continue;
    }
    if (dist_comped_vec.at(i) < board_info.raw_dist_min)
    {
      continue;
    }
    static_dist_vec.at(code_mark).emplace_back(dist_comped_vec.at(i) - board_info.distance_05cm);
    static_area_vec.at(code_mark).emplace_back(dist_area_map.area_vec.at(i));
  }

  for (size_t code_mark = 0; code_mark < static_dist_vec.size(); ++code_mark)
  {
    size_t center_index = static_dist_vec.at(code_mark).size() / 2;
    std::vector<float> dist_vec;
    std::vector<float> dist_index_vec;
    float index = 0;
    for (size_t i = center_index - 25; i < center_index + 25; ++i)
    {
      dist_vec.emplace_back(static_dist_vec.at(code_mark).at(i));
      dist_index_vec.emplace_back(index);
      index += 1;
    }

    AnalyzeUtils::eraseOutlier(dist_index_vec, dist_vec, 3, 0.5);
    auto mean_dist = std::accumulate(dist_vec.begin(), dist_vec.end(), 0.0) / static_cast<double>(dist_vec.size());
    dist_area_map.static_comp_result.at(code_mark) = static_cast<float>(mean_dist);
  }

  // 补偿静标
  for (size_t i = 0; i < dist_comped_vec.size(); ++i)
  {
    auto& dist       = dist_comped_vec.at(i);
    auto code_mark   = dist_area_map.code_mark_vec.at(i);
    auto static_comp = dist_area_map.static_comp_result.at(code_mark);
    if (dist >= static_comp)
    {
      dist -= static_comp;
    }
    else
    {
      dist = 0;
    }
  }

  dist_area_map.is_dynamic_static_comp = true;
  return true;
}

bool AiryWaveSignalModel::processReflData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 反标数据", _channel_num));
  auto& dist_area_map   = getDistAreaMap(_channel_num);
  auto& board_info_map  = getDistAreaMap(_channel_num).board_info_map;
  auto& dist_comped_vec = dist_area_map.dist_comped_vec;
  auto& area_vec        = dist_area_map.area_vec;
  auto& amp_vec         = dist_area_map.amp_vec;

  if (dist_area_map.static_comp_result.empty())
  {
    LOG_ERROR("static comp result is empty, channel num: {}", _channel_num);
    return false;
  }

  // fix10mBoard(_channel_num);
  detectReflBoardCenter(_channel_num);

  std::map<BoardRefl, PointsInt> refl_dist_area_points_map;
  std::map<BoardRefl, PointsInt> refl_dist_amp_points_map;

  for (const auto& board_id : refl_board_id_vec_)
  {
    const auto& board_info = board_info_map.at(board_id);
    for (const auto& board_pos : board_info.refl_board_vec)
    {
      auto refl = board_pos.refl;
      if (board_pos.area_index == -1)
      {
        LOG_ERROR("board_id: {}, refl: {} pos_index is -1", board_id, refl);
        continue;  // 若无效索引则跳过
      }

      auto area_index = board_pos.area_index;
      auto amp_index  = board_pos.amp_index;
      auto area       = area_vec.at(area_index);
      auto amp        = amp_vec.at(amp_index);

      auto& area_points = refl_dist_area_points_map[refl];
      auto& amp_points  = refl_dist_amp_points_map[refl];
      auto dist_mean    = board_pos.dist_mean;
      auto area_mean    = board_pos.area_mean_code_1;
      auto amp_mean     = board_pos.amp_mean_code_1;

      // 插入处理后的数据点
      area_points.x_vec.push_back(dist_mean);
      area_points.y_vec.push_back(area_mean);

      amp_points.x_vec.push_back(dist_mean);
      amp_points.y_vec.push_back(amp_mean);
    }
  }
  for (auto& [refl, points] : refl_dist_area_points_map)
  {
    points.sortByX();
  }
  for (auto& [refl, points] : refl_dist_amp_points_map)
  {
    points.sortByX();
  }

  if (refl_dist_area_points_map[255].y_vec.size() < 4 || refl_dist_area_points_map[90].y_vec.size() < 4)
  {
    LOG_ERROR("通道: {} 反标数据异常，90反射率或255反射率数据点数量不足，无法进行等效", _channel_num);
  }
  else
  {
    equivalentDistRefl(_channel_num, refl_dist_area_points_map);
    equivalentDistAmp(refl_dist_amp_points_map);
  }

  for (auto& [refl, points] : refl_dist_area_points_map)
  {
    points.sortByX();
    auto& refl_comp_fit_result = dist_area_map.area_comp_fit_result_map[refl];
    refl_comp_fit_result.resize(32768);
    interpolateRefl(refl, points);
    linearInterpolation(refl, points, refl_comp_fit_result);
    // for (size_t i = 9000; i < refl_comp_fit_result.size(); i++)
    // {
    //   refl_comp_fit_result.at(i) = 0;
    // }
  }
  for (auto& [refl, points] : refl_dist_amp_points_map)
  {
    points.sortByX();
    auto& refl_comp_fit_result = dist_area_map.amp_comp_fit_result_map[refl];
    refl_comp_fit_result.resize(32768);

    linearInterpolation(refl, points, refl_comp_fit_result);
    // for (size_t i = 9000; i < refl_comp_fit_result.size(); i++)
    // {
    //   refl_comp_fit_result.at(i) = 0;
    // }
  }

  return true;
}

bool AiryWaveSignalModel::processAbsData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 绝标数据", _channel_num));
  auto& dist_area_map   = getDistAreaMap(_channel_num);
  auto& board_info_map  = getDistAreaMap(_channel_num).board_info_map;
  auto& dist_comped_vec = dist_area_map.dist_comped_vec;
  auto& dist_true_vec   = dist_area_map.dist_true_vec;
  auto& dist_error_vec  = dist_area_map.dist_error_vec;
  auto& coe_k_vec       = dist_area_map.abs_coe_k_vec;
  auto& coe_b_vec       = dist_area_map.abs_coe_b_vec;
  auto& dist_test_vec   = dist_area_map.dist_test_vec;
  dist_true_vec.clear();
  dist_test_vec.clear();
  dist_error_vec.clear();
  coe_k_vec.clear();
  coe_b_vec.clear();

  for (const auto& board_id : abs_board_id_vec_)
  {
    auto board_info = board_info_map[board_id];
    if (!board_info.is_found)
    {
      LOG_ERROR("abs target_id: {} not found", board_info.board_id);
      return false;
    }
    size_t start = board_info.detected_data_start;
    size_t end   = board_info.detected_data_end;
    if ((end - start) <= 100)
    {
      LOG_ERROR("绝标靶板数据小于100，无法提取中心前后50个点，通道：{}，靶板：{}", _channel_num, board_id);
      return false;
    }

    size_t center = start + (end - start) / 2;

    std::vector<float> area_vec;
    std::vector<float> dist_vec;
    for (size_t i = center - 50; i < center + 50; ++i)
    {
      area_vec.push_back(static_cast<float>(dist_area_map.area_vec.at(i)));
      dist_vec.push_back(dist_comped_vec.at(i));
    }
    AnalyzeUtils::eraseOutlier(area_vec, dist_vec, 5, 0.85);
    float mean_dist = std::accumulate(dist_vec.begin(), dist_vec.end(), 0.0) / static_cast<float>(dist_vec.size());

    auto dist_true = board_info.distance_05cm;
    auto dist_err  = dist_true - mean_dist;

    dist_error_vec.push_back(static_cast<float>(dist_err));
    dist_test_vec.push_back(mean_dist);
    dist_true_vec.push_back(dist_true);

    // LOG_INFO("board_id: {}, dist_true: {}, mid_value: {}, dist_err: {}", board_id, dist_true, mid_value,
    // dist_err);
  }
  // 求斜率和截距
  size_t curr_size = dist_true_vec.size();
  for (size_t i = 0; i < curr_size - 1; ++i)
  {
    auto coe_k = (dist_error_vec.at(i + 1) - dist_error_vec.at(i)) / (dist_true_vec.at(i + 1) - dist_true_vec.at(i));
    auto coe_b = dist_error_vec.at(i);
    coe_k_vec.push_back(coe_k);
    coe_b_vec.push_back(coe_b);
  }
  for (size_t i = curr_size; i <= 8; ++i)
  {
    coe_k_vec.push_back(0);
    coe_b_vec.push_back(dist_error_vec.back());
  }

  auto distance_end = 60 * 1000 / 5;
  std::vector<float> abs_comp_vec;
  abs_comp_vec.reserve(distance_end);

  for (size_t i = 0; i < distance_end; ++i)
  {
    float current_dist = static_cast<float>(i);

    if (current_dist >= dist_true_vec.back())
    {
      // 超出右边界
      abs_comp_vec.push_back(dist_error_vec.back());
    }
    else if (current_dist <= dist_true_vec.front())
    {
      // 超出左边界
      abs_comp_vec.push_back(dist_error_vec.front());
    }
    else
    {
      // 找到当前点在 dist_true_vec 中的位置
      auto iter    = std::lower_bound(dist_true_vec.begin(), dist_true_vec.end(), current_dist);
      size_t index = std::distance(dist_true_vec.begin(), iter) - 1;

      // 计算插值结果
      auto coe_k     = coe_k_vec.at(index);
      auto coe_b     = coe_b_vec.at(index);
      auto dist_comp = (coe_k * (current_dist - dist_true_vec.at(index))) + coe_b;

      abs_comp_vec.push_back(dist_comp);
    }
  }
  for (auto& dist : dist_comped_vec)
  {
    if (roundToInt(dist) >= distance_end)
    {
      continue;
    }
    dist += abs_comp_vec.at(roundToInt(dist));
  }

  return true;
}

void AiryWaveSignalModel::backupBoardInfoMap(const int _channel_num)
{
  auto& dist_area_map              = getDistAreaMap(_channel_num);
  dist_area_map.board_info_map_bak = dist_area_map.board_info_map;
}

bool AiryWaveSignalModel::checkReflAmpData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 反标检查", _channel_num));
  auto& area_comp_fit_result_map = getDistAreaMap(_channel_num).area_comp_fit_result_map;
  auto& amp_comp_fit_result_map  = getDistAreaMap(_channel_num).amp_comp_fit_result_map;

  std::vector<int> refl_vec;
  refl_vec.reserve(area_comp_fit_result_map.size());
  for (const auto& [refl, fit_result_vec] : area_comp_fit_result_map)
  {
    refl_vec.push_back(refl);
  }

  if (refl_vec.size() != 4)
  {
    LOG_ERROR("反标数据检查异常，通道: {}, 反标反射率数量: {}", _channel_num, refl_vec);
    return false;
  }

  auto area_data_size        = static_cast<int>(area_comp_fit_result_map[10].size());
  auto area_center_data_size = static_cast<int>(area_comp_fit_result_map[10].size() / 2);

  auto index_20m = static_cast<int>(20. / 5 * 1000);

  for (int i = 0; i < area_data_size; ++i)
  {
    for (int j = 0; j < refl_vec.size() - 1; ++j)
    {
      auto curr_refl = refl_vec.at(j);
      auto next_refl = refl_vec.at(j + 1);

      auto curr_area = area_comp_fit_result_map[curr_refl].at(i);
      auto next_area = area_comp_fit_result_map[next_refl].at(i);

      if (i < index_20m)
      {
        if (curr_area == 0 || next_area == 0)
        {
          LOG_ERROR("反标数据检查异常，通道: {}, {}m 反标反射率{}的index: {}的数据不能为0", _channel_num,
                    static_cast<float>(i) / area_data_size * 60, curr_refl, i);
          return false;
        }
      }

      if (curr_area > next_area)
      {
        LOG_ERROR("反标数据检查异常，通道: {}, 反标反射率{}与{}非递增趋势，index: {}", _channel_num, next_refl,
                  curr_refl, i);
        return false;
      }
    }
  }

  auto amp_data_size        = static_cast<int>(area_comp_fit_result_map[10].size());
  auto amp_center_data_size = static_cast<int>(area_comp_fit_result_map[10].size() / 2);

  int amp_max   = 0;
  auto index_3m = static_cast<int>(3. / 5 * 1000);

  for (int i = 0; i < amp_data_size; ++i)
  {
    if (i < index_3m)
    {
      continue;
    }
    for (int j = 0; j < refl_vec.size() - 1; ++j)
    {
      auto curr_refl = refl_vec.at(j);
      auto next_refl = refl_vec.at(j + 1);

      auto curr_amp = amp_comp_fit_result_map[curr_refl].at(i);
      auto next_amp = amp_comp_fit_result_map[next_refl].at(i);

      if (i < index_20m)
      {
        if (curr_amp == 0 || next_amp == 0)
        {
          LOG_ERROR("幅值反标数据检查异常，通道: {}, {}m 幅值{}的index: {}的数据不能为0", _channel_num,
                    static_cast<float>(i) / amp_data_size * 60, curr_refl, i);
          return false;
        }
      }

      if (curr_amp > (next_amp + 20))
      {
        LOG_ERROR("幅值反标数据检查异常，通道: {}, 幅值{}与{}非递增趋势，index: {}", _channel_num, next_refl, curr_refl,
                  i);
        return false;
      }
      amp_max = std::max(amp_comp_fit_result_map[curr_refl].at(i), amp_max);
    }
  }

  std::string amp_max_limit_name = "area_amp_chn1_8";

  if (_channel_num >= 9 && _channel_num <= 32)
  {
    amp_max_limit_name = "area_amp_chn9_32";
  }
  else if (_channel_num >= 33 && _channel_num <= 64)
  {
    amp_max_limit_name = "area_amp_chn33_64";
  }
  else if (_channel_num >= 65 && _channel_num <= 88)
  {
    amp_max_limit_name = "area_amp_chn65_88";
  }
  else if (_channel_num >= 89 && _channel_num <= 96)
  {
    amp_max_limit_name = "area_amp_chn89_96";
  }

  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(amp_max_limit_name);
  if (!limit_info.is_ok)
  {
    LOG_ERROR("配置文件出错，无法找到limit_info name: {}, chn: {}", amp_max_limit_name, _channel_num);
    return false;
  }

  if (!WidgetLogSetting::checkWithinLimit(limit_info, amp_max, rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    LOG_ERROR("幅值超过最大阈值，通道: {}, 幅值: {}", _channel_num, amp_max);
    addMeasureMessage(limit_info, amp_max, rsfsc_lib::MEASURE_DATA_TYPE_INT);
    return false;
  }

  return true;
}

bool AiryWaveSignalModel::checkDistReflData(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 检查测距", _channel_num));
  auto& dist_area_map = getDistAreaMap(_channel_num);

  // if (!detectBoardUseTrueDist(_channel_num))
  // {
  //   return false;
  // }

  if (!checkBoardDistRefl(_channel_num))
  {
    return false;
  }

  // 检查真值靶板距离

  return true;
}

bool AiryWaveSignalModel::checkBoardDistRefl(const int _channel_num)
{
  ScopedTimer timer(fmt::format("通道 {} 检查靶板距离", _channel_num));
  auto& dist_area_map  = getDistAreaMap(_channel_num);
  auto& board_info_map = dist_area_map.board_info_map;

  QStringList check_data_list;

  bool res = true;
  for (const auto& [board_id, board_info] : board_info_map)
  {
    for (const auto& refl_board_pos : board_info.refl_board_vec)
    {
      if (refl_board_pos.distance_05cm < 0)
      {
        continue;
      }
      float true_value = board_info.distance_05cm;
      if (refl_board_pos.distance_05cm > 0)
      {
        true_value = refl_board_pos.distance_05cm;
      }
      true_value *= 5;
      auto dist_mean_value = refl_board_pos.dist_mean * 5;
      auto err_sd          = refl_board_pos.dist_err_sd * 5;

      auto refl_value = transformAreaToRefl(_channel_num, refl_board_pos.area_mean, refl_board_pos.dist_mean);

      auto err_value = dist_mean_value - true_value;
      check_data_list << fmt::format("{},{}mm,{},{},{:.1f},{:.1f},{:.1f},{}", _channel_num, board_info.distance,
                                     refl_board_pos.refl, true_value, dist_mean_value, err_value, err_sd, refl_value)
                           .c_str();
    }
  }

  auto file_name = fmt::format(path_.comped_eval_file_name.toStdString(), "");
  auto file_path = path_.result_dir.absoluteFilePath(file_name.c_str());
  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text | QIODevice::Append))
  {
    LOG_ERROR("打开文件失败, 文件路径: {}", file_path);
    return false;
  }
  QTextStream out(&file);
  for (const auto& data : check_data_list)
  {
    out << data << "\n";
  }
  file.close();

  return res;
}

uint8_t AiryWaveSignalModel::transformAreaToRefl(const int _channel_num, const float _area, const float _dist)
{
  auto& dist_area_map            = getDistAreaMap(_channel_num);
  auto& area_comp_fit_result_map = dist_area_map.area_comp_fit_result_map;

  int index = roundToInt(_dist);
  if (index > 32767)
  {
    return 0;
  }

  float refl_left_range  = 0;
  float refl_right_range = 0;
  float area_left_range  = 0;
  float area_right_range = 0;
  for (const auto& [refl, area_vec] : area_comp_fit_result_map)
  {
    if (index >= area_vec.size())
    {
      return 0;
    }
    area_right_range = static_cast<float>(area_vec.at(index));
    if (_area <= area_right_range)
    {
      refl_right_range = static_cast<float>(refl);
      float refl_range = refl_right_range - refl_left_range;
      float area_range = area_right_range - area_left_range;
      float refl_value = ((_area - area_left_range) / area_range * refl_range) + refl_left_range;
      return static_cast<uint8_t>(roundToInt(refl_value));
    }

    refl_left_range = static_cast<float>(refl);
    area_left_range = area_right_range;
  }

  return 255;
}

uint16_t AiryWaveSignalModel::sign2fi(const double& _val, const int& _width_bit, const int& _frac_bit)
{
  auto data_out = static_cast<int>(std::round(_val * std::pow(2, _frac_bit)));
  auto data_max = static_cast<int>(std::pow(2, _width_bit - 1) - 1);
  auto data_min = static_cast<int>(-std::pow(2, _width_bit - 1));
  data_out      = std::min(data_out, data_max);
  data_out      = std::max(data_out, data_min);
  if (data_out < 0)
  {
    data_out = static_cast<int>(std::pow(2, _width_bit) + data_out);
  }
  // int16_t test = static_cast<int16_t>(data_out);
  return static_cast<uint16_t>(data_out);
}

void AiryWaveSignalModel::equivalentDistRefl(const int _channel_num,
                                             std::map<BoardRefl, PointsInt>& _dist_area_points_map)
{
  // 将90反射率从低4个点开始，往上挪到255反射率的高4个点
  int refl_board_3m_index  = 2;
  int refl_board_10m_index = 3;
  int refl_board_20m_index = 4;

  auto& area_points_10  = _dist_area_points_map[10].y_vec;
  auto& area_points_40  = _dist_area_points_map[40].y_vec;
  auto& area_points_90  = _dist_area_points_map[90].y_vec;
  auto& area_points_255 = _dist_area_points_map[255].y_vec;
  auto& dist_points_10  = _dist_area_points_map[10].x_vec;
  auto& dist_points_40  = _dist_area_points_map[40].x_vec;
  auto& dist_points_90  = _dist_area_points_map[90].x_vec;
  auto& dist_points_255 = _dist_area_points_map[255].x_vec;

  // 在20m处，将90反射率的面积上移到255反射率的面积
  int delta_area = static_cast<int>(area_points_255.at(refl_board_10m_index)) -
                   static_cast<int>(area_points_90.at(refl_board_10m_index));
  for (size_t i = refl_board_20m_index; i < dist_points_90.size(); i++)
  {
    int val = area_points_90.at(i) + delta_area;
    dist_points_255.emplace_back(dist_points_90.at(i));
    area_points_255.emplace_back(val);
  }

  auto high_refl_delta = block_3m_refl_delta_vec_.at(((_channel_num - 1) / 8));

  // 3m 处的 255高反，根据叠加次数+反标数值
  area_points_255.at(refl_board_3m_index) = area_points_90.at(refl_board_3m_index) + high_refl_delta;

  // 3m 处的反射率，限制在32768以内
  area_points_255.at(refl_board_3m_index) = std::min(area_points_255.at(refl_board_3m_index), 32768);
  area_points_90.at(refl_board_3m_index)  = std::min(area_points_90.at(refl_board_3m_index), 32768);
  area_points_40.at(refl_board_3m_index)  = std::min(area_points_40.at(refl_board_3m_index), 32768);
  area_points_10.at(refl_board_3m_index)  = std::min(area_points_10.at(refl_board_3m_index), 32768);
}

void AiryWaveSignalModel::interpolateRefl(const BoardRefl _refl, PointsInt& _raw_points)
{
  // 10%的延伸面积参数为：[3103 2131 1464 1006 691 474 326 224 154]
  // 40%的延伸面积参数为：[9613 7253 5414 4028 3027 2340 1900 1637 1483]
  // 90%的延伸面积参数为：[13688 11507 9609 7974 6580 5408 4438 3648 3019]
  auto& area_points = _raw_points.y_vec;
  auto& dist_points = _raw_points.x_vec;
  // 求出比例K
  std::vector<float> k_vec;
  switch (_refl)
  {
  case 10:
  {
    for (size_t i = 1; i < REFL10_INTERPOLATION_VAL.size(); i++)
    {
      k_vec.emplace_back(static_cast<float>(REFL10_INTERPOLATION_VAL.at(i)) / REFL10_INTERPOLATION_VAL.at(0));
    }
    break;
  }
  case 40:
  {
    for (size_t i = 1; i < REFL40_INTERPOLATION_VAL.size(); i++)
    {
      k_vec.emplace_back(static_cast<float>(REFL40_INTERPOLATION_VAL.at(i)) / REFL40_INTERPOLATION_VAL.at(0));
    }
    break;
  }
  case 90:
  {
    for (size_t i = 1; i < REFL90_INTERPOLATION_VAL.size(); i++)
    {
      k_vec.emplace_back(static_cast<float>(REFL90_INTERPOLATION_VAL.at(i)) / REFL90_INTERPOLATION_VAL.at(0));
    }
    break;
  }
  case 255:
  {
    for (size_t i = 1; i < REFL90_INTERPOLATION_VAL.size(); i++)
    {
      k_vec.emplace_back(static_cast<float>(REFL90_INTERPOLATION_VAL.at(i)) / REFL90_INTERPOLATION_VAL.at(0));
    }
    break;
  }
  default: LOG_ERROR("插值比例K计算失败, 反射率: {}", _refl); break;
  }

  // 以最后一个点为基准，计算插入，横坐标每隔1000进行插入
  auto base_dist = dist_points.back();
  auto base_area = area_points.back();

  for (auto k_val : k_vec)
  {
    base_dist += 1000;
    dist_points.emplace_back(roundToInt(base_dist));
    area_points.emplace_back(roundToInt(roundToInt(k_val * static_cast<float>(base_area))));
  }
}

void AiryWaveSignalModel::equivalentDistAmp(std::map<BoardRefl, PointsInt>& _dist_amp_points_map)
{
  auto refl10_board20m_index = _dist_amp_points_map[10].x_vec.size() - 1;
  auto refl10_board10m_index = _dist_amp_points_map[10].x_vec.size() - 2;
  auto refl10_board20m_dist  = _dist_amp_points_map[10].x_vec.at(refl10_board20m_index);
  auto refl10_board10m_dist  = _dist_amp_points_map[10].x_vec.at(refl10_board10m_index);

  // amp等效
  auto refl10_board20m_amp = _dist_amp_points_map[10].y_vec.at(refl10_board20m_index);
  auto refl10_board10m_amp = _dist_amp_points_map[10].y_vec.at(refl10_board10m_index);
  _dist_amp_points_map[40].x_vec.emplace_back(refl10_board20m_dist * 2);
  _dist_amp_points_map[40].y_vec.emplace_back(refl10_board20m_amp);
  _dist_amp_points_map[90].x_vec.emplace_back(refl10_board10m_dist * 3);
  _dist_amp_points_map[90].y_vec.emplace_back(refl10_board10m_amp);
  _dist_amp_points_map[90].x_vec.emplace_back(refl10_board20m_dist * 3);
  _dist_amp_points_map[90].y_vec.emplace_back(refl10_board20m_amp);

  int refl_board_3m_index  = 2;
  int refl_board_10m_index = 3;
  int refl_board_20m_index = 4;
  // int delta_amp            = static_cast<int>(_refl_dist_amp_points_map[255].y_vec.at(refl_board_10m_index)) -
  //                 static_cast<int>(_refl_dist_amp_points_map[90].y_vec.at(refl_board_10m_index));
  for (size_t i = refl_board_20m_index; i < _dist_amp_points_map[90].x_vec.size(); i++)
  {
    int val = _dist_amp_points_map[90].y_vec.at(i) + 200;
    _dist_amp_points_map[255].x_vec.emplace_back(_dist_amp_points_map[90].x_vec.at(i));
    _dist_amp_points_map[255].y_vec.emplace_back(val);
  }
}

bool AiryWaveSignalModel::linearInterpolation(const BoardRefl _refl,
                                              const PointsInt& _raw_points,
                                              std::vector<int>& _fit_res_points)
{
  // 范围内的线性插值，范围外的直接拉平
  for (size_t i = 0; i < _raw_points.x_vec.size() - 1; ++i)
  {
    auto start = _raw_points.x_vec.at(i);
    auto end   = _raw_points.x_vec.at(i + 1);
    if (start < 0 || end < 0)
    {
      LOG_ERROR("线性插值失败，start: {}, end: {}", start, end);
      return false;
    }
    auto slope = static_cast<double>(_raw_points.y_vec.at(i + 1) - _raw_points.y_vec.at(i)) /
                 static_cast<double>(_raw_points.x_vec.at(i + 1) - _raw_points.x_vec.at(i));
    auto bias = static_cast<double>(_raw_points.y_vec.at(i)) - slope * static_cast<double>(_raw_points.x_vec.at(i));

    for (auto index = start; index < end && index < _fit_res_points.size(); ++index)
    {
      _fit_res_points.at(index) = roundToInt(slope * index + bias);
    }
  }

  // 拉平范围外的数据
  for (size_t i = 0; i < _raw_points.x_vec.front(); ++i)
  {
    _fit_res_points.at(i) = _raw_points.y_vec.front();
  }

  if (_refl == 10 || _refl == 40)
  {
    // 最后两个点的线性外推
    auto slope = static_cast<double>(_raw_points.y_vec.back() - _raw_points.y_vec.at(_raw_points.x_vec.size() - 2)) /
                 static_cast<double>(_raw_points.x_vec.back() - _raw_points.x_vec.at(_raw_points.x_vec.size() - 2));
    auto bias = static_cast<double>(_raw_points.y_vec.at(_raw_points.x_vec.size() - 2)) -
                slope * static_cast<double>(_raw_points.x_vec.at(_raw_points.x_vec.size() - 2));
    for (size_t value = _raw_points.x_vec.back(); value < _fit_res_points.size(); ++value)
    {
      double interpolation_value = slope * static_cast<float>(value) + bias;

      if (interpolation_value < 0)
      {
        _fit_res_points.at(value) = 0;
      }
      else if (interpolation_value > 65535)
      {
        _fit_res_points.at(value) = 65535;
      }
      else
      {
        _fit_res_points.at(value) = static_cast<uint16_t>(interpolation_value);
      }
    }
    return true;
  }

  for (size_t i = _raw_points.x_vec.back(); i < _fit_res_points.size(); ++i)
  {
    _fit_res_points.at(i) = _raw_points.y_vec.back();
  }
  return true;
}

bool AiryWaveSignalModel::autoProcessAllData()
{
  ScopedTimer timer("auto process all data");
  updateAreaType(AreaType::UNKNOWN);
  if (dist_area_map_.empty())
  {
    LOG_ERROR("数据未加载");
    return false;
  }
  if (!path_.data_dir.exists() || path_.dynamic_bit_file_name.isEmpty() || path_.static_bit_file_name.isEmpty() ||
      path_.refl_bit_file_name.isEmpty() || path_.abs_bit_file_name.isEmpty())
  {
    LOG_ERROR("bit文件路径为空, data dir: {}, dynamic: {}, static: {}, refl: {}", path_.data_dir.absolutePath(),
              path_.dynamic_bit_file_name, path_.static_bit_file_name, path_.refl_bit_file_name,
              path_.abs_bit_file_name);
    return false;
  }

  auto file_name = fmt::format(path_.comped_eval_file_name.toStdString(), "");
  auto file_path = path_.result_dir.absoluteFilePath(file_name.c_str());
  QFile file(file_path);
  if (file.exists())
  {
    file.remove();
  }
  QString header = "通道,距离,真值反射率,真值,均值,均值误差,误差标准差,反射率";
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    LOG_ERROR("打开文件失败, 文件路径: {}", file_path);
    return false;
  }
  QTextStream out(&file);
  out << header << "\n";
  file.close();

  int total_task = total_chn_num_;
  app()->signalUpdateProgressProcessTotalTask(total_task);
  app()->signalUpdateProgressSaveProcessDataTotalTask(total_task);
  std::map<ChannelNumType, std::string> error_map;
  for (auto chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    app()->signalUpdateProgressProcessData(chn_num - 1);
    // app()->signalUpdateProgressSaveProcessData(channel_num - 1);
    if (!detectDynamicBoard(chn_num))
    {
      LOG_ERROR("筛选动标靶板失败, 通道: {}", chn_num);
      error_map[chn_num] = "筛选动标靶板失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!detect10mBoard(chn_num))
    {
      LOG_ERROR("检测10m靶板失败, 通道: {}", chn_num);
      error_map[chn_num] = "检测10m靶板失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!detect20mBoard(chn_num))
    {
      LOG_ERROR("检测20m靶板失败, 通道: {}", chn_num);
      error_map[chn_num] = "检测20m靶板失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!processDynamicData(chn_num))
    {
      LOG_ERROR("动标数据处理过程出现失败, 通道: {}", chn_num);
      error_map[chn_num] = "动标数据处理过程出现失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!detectStaticBoard(chn_num))
    {
      LOG_ERROR("检测静标板失败, 通道: {}", chn_num);
      error_map[chn_num] = "检测静标板失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!processStaticData(chn_num))
    {
      LOG_ERROR("拟合静标数据失败, 通道: {}", chn_num);
      error_map[chn_num] = "拟合静标数据失败";
      backupBoardInfoMap(chn_num);
    }

    if (!detectBoard(chn_num))
    {
      LOG_ERROR("检测靶板失败, 通道: {}", chn_num);
      error_map[chn_num] = "检测靶板失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    if (!processAbsData(chn_num))
    {
      LOG_ERROR("处理绝标数据失败, 通道: {}", chn_num);
      error_map[chn_num] = "拟合绝标数据失败";
      backupBoardInfoMap(chn_num);
    }

    if (!fixBoardCompedDist(chn_num))
    {
      LOG_ERROR("修正靶板距离失败, 通道: {}", chn_num);
      error_map[chn_num] = "修正靶板距离失败";
      backupBoardInfoMap(chn_num);
    }

    if (!processReflData(chn_num))
    {
      LOG_ERROR("拟合反标数据失败, 通道: {}", chn_num);
      error_map[chn_num] = "拟合反标数据失败";
      backupBoardInfoMap(chn_num);
      continue;
    }

    backupBoardInfoMap(chn_num);

    if (!checkReflAmpData(chn_num))
    {
      LOG_ERROR("反标数据检查异常, 通道: {}", chn_num);
      error_map[chn_num] = "反标数据检查异常";
    }

    if (getParaInfo()->getEvalResult())
    {
      if (!detectBoardUseTrueDist(chn_num))
      {
        LOG_ERROR("通道 {} 校验真值筛选靶板失败", chn_num);
        error_map[chn_num] = "校验真值筛选靶板失败";
      }
      if (!checkDistReflData(chn_num))
      {
        LOG_ERROR("通道 {} 距离数据检查异常", chn_num);
        error_map[chn_num] = "距离数据检查异常";
      }

      // if (!saveDistAreaMap(channel_num))
      // {
      //   LOG_ERROR("保存标定数据失败, 通道: {}", channel_num);
      //   error_map[channel_num] = "保存标定数据失败";
      // }
      // if (!saveInfo(channel_num))
      // {
      //   LOG_ERROR("保存靶板数据失败, 通道: {}", channel_num);
      //   error_map[channel_num] = "保存靶板数据失败";
      // }
    }
  }
  app()->signalUpdateProgressProcessData(total_task);
  // app()->signalUpdateProgressSaveProcessData(total_task);

  saveAllProcessData();
  save90Refl10m();

  if (!dynamic_len_not_enough_chn_vec_.empty())
  {
    auto err_msg = fmt::format("动标靶板长度不足的通道: {}", dynamic_len_not_enough_chn_vec_);
    app()->signalShowErrorText(err_msg.c_str());
    LOG_INDEX_ERROR(err_msg);
  }

  if (!zero_area_chn_vec_.empty())
  {
    auto err_msg = fmt::format("积分值为0的通道: {}", zero_area_chn_vec_);
    app()->signalShowErrorText(err_msg.c_str());
    LOG_INDEX_ERROR(err_msg);
  }

  // LOG_INDEX_INFO("chn_angle_vec_: {}", chn_angle_vec_);

  if (!error_map.empty())
  {
    for (auto& [channel_num, error] : error_map)
    {
      LOG_ERROR("通道: {}, 错误: {}", channel_num, error);
    }
    return false;
  }

  LOG_INFO("生成bit文件");
  if (!generateAllBit())
  {
    LOG_ERROR("生成bit文件失败");
    error_map[0] += "生成bit文件失败";
  }

  if (product_area_type_ != AreaType::PASS)
  {
    // error_map[0] = "总积分值判定为NG,";
    LOG_INDEX_ERROR("总积分值判定为{}", product_area_type_);
    return false;
  }

  return error_map.empty();
}

bool AiryWaveSignalModel::generateAllBit()
{
  bool success = true;
  CombineBit combine_bit {};

  QString dynamic_bit_file_path = path_.data_dir.absoluteFilePath(path_.dynamic_bit_file_name);
  QString static_bit_file_path  = path_.data_dir.absoluteFilePath(path_.static_bit_file_name);
  QString refl_bit_file_path    = path_.data_dir.absoluteFilePath(path_.refl_bit_file_name);
  QString abs_bit_file_path     = path_.data_dir.absoluteFilePath(path_.abs_bit_file_name);

  if (!generateDynamicBit(combine_bit.dynamic_bit, dynamic_bit_file_path))
  {
    LOG_ERROR("generate dynamic bin failed");
    success = false;
  }
  if (!generateStaticBit(combine_bit.static_bit, static_bit_file_path))
  {
    LOG_ERROR("generate static bin failed");
    success = false;
  }
  if (!generateReflBit(combine_bit.refl_bit, refl_bit_file_path))
  {
    LOG_ERROR("generate refl bin failed");
    success = false;
  }
  if (!generateAbsBit(combine_bit.abs_bit, abs_bit_file_path))
  {
    LOG_ERROR("generate abs bin failed");
    success = false;
  }
  // if (!getEmpAbsBit(combine_bit.emp_abs_bit))
  // {
  //   LOG_ERROR("read emp abs bin failed");
  //   success = false;
  // }
  // combine_bit.toBigEndian();

  combine_bit.refl_crc    = CrcUtils::calcCrc(combine_bit.refl_bit.arr.data(), combine_bit.refl_bit.arr.size());
  combine_bit.dynamic_crc = CrcUtils::calcCrc(combine_bit.dynamic_bit.arr.data(), combine_bit.dynamic_bit.arr.size());
  combine_bit.static_crc  = CrcUtils::calcCrc(combine_bit.static_bit.arr.data(), combine_bit.static_bit.arr.size());
  combine_bit.abs_crc     = CrcUtils::calcCrc(combine_bit.abs_bit.arr.data(), combine_bit.abs_bit.arr.size());
  // combine_bit.emp_abs_crc = CrcUtils::calcCrc(combine_bit.emp_abs_bit.arr.data(), combine_bit.emp_abs_bit.arr.size());

  combine_bit_ = combine_bit;

  if (success)
  {
    LOG_INFO("combine bin now ...");

    QString file_name = path_.combine_bit_file_name;
    QString file_path = path_.data_dir.absoluteFilePath(file_name);
    if (!saveFile(file_path, combine_bit.arr.data(), sizeof(combine_bit)))
    {
      return false;
    }
    combine_bit_file_path_ = file_path;
    LOG_INFO("生成combine bit文件成功, file path: {}", file_path.toStdString());
  }

  return success;
}

bool AiryWaveSignalModel::saveFile(const QString& _save_path, const char* _data, const int _size)
{
  QFile file(_save_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("open file failed, file path: {}", _save_path.toStdString());
    return false;
  }
  if (file.write(_data, _size) != _size)
  {
    LOG_ERROR("write file failed, file path: {}", _save_path.toStdString());
    return false;
  }
  return true;
}

bool AiryWaveSignalModel::generateDynamicBit(DynamicBit& _dynamic_bit, const QString& _save_path)
{
  DynamicBit dynamic_bit {};

  std::vector<uint16_t> dynamic_comp_area_arr;

  dynamic_comp_area_arr.insert(dynamic_comp_area_arr.end(), DYNAMIC_COMP_AREA_ARR.begin(), DYNAMIC_COMP_AREA_ARR.end());

  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    auto comp_index           = chn_num - 1;
    auto& dist_area_map       = getDistAreaMap(chn_num);
    auto& dynamic_comp_result = dist_area_map.dynamic_comp_result.at(dist_area_map.dynamic_code_mark);
    if (dynamic_comp_result.empty())
    {
      LOG_ERROR("channel_num: {} dynamic data not found", chn_num);
      return false;
    }

    for (size_t calib_area_index = 0; calib_area_index < dynamic_comp_area_arr.size(); ++calib_area_index)
    {
      auto calib_area                                                   = dynamic_comp_area_arr.at(calib_area_index);
      auto dist_value                                                   = dynamic_comp_result.at(calib_area);
      dynamic_bit.comp.at(comp_index).dist_val_arr.at(calib_area_index) = roundToInt(dist_value);
    }
    ++comp_index;
  }

  std::vector<uint8_t> gdi_reg_val;
  for (int chn_group = 1; chn_group <= dynamic_bit.gdi_reg.size() * 8; chn_group += 8)
  {
    uint8_t reg_val = 0xff;
    std::bitset<8> bits(reg_val);
    for (uint16_t i = 0; i < 8; ++i)
    {
      if (getDistAreaMap(chn_group + i).refl90_is_min_area)
      {
        bits.reset(i);
      }
    }
    reg_val = static_cast<uint8_t>(bits.to_ulong());
    gdi_reg_val.emplace_back(reg_val);
  }
  std::reverse(gdi_reg_val.begin(), gdi_reg_val.end());
  for (int i = 0; i < dynamic_bit.gdi_reg.size(); ++i)
  {
    dynamic_bit.gdi_reg.at(i).reg_addr     = DYNAMIC_GDI_REG_START >> 8U;
    dynamic_bit.gdi_reg.at(i).reg_addr_val = (((DYNAMIC_GDI_REG_START + i) & 0xffU) << 8U) + gdi_reg_val.at(i);
  }

  dynamic_bit.toBigEndian();

  if (!saveFile(_save_path, dynamic_bit.arr.data(), sizeof(dynamic_bit)))
  {
    return false;
  }

  LOG_INFO("生成动标bit文件成功, file path: {}", _save_path.toStdString());
  _dynamic_bit = dynamic_bit;
  return true;
}

bool AiryWaveSignalModel::generateStaticBit(StaticBit& _static_bit, const QString& _save_path)
{
  StaticBit static_bit {};

  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    auto& static_comp_result = getDistAreaMap(chn_num).static_comp_result;

    auto& comp = static_bit.comp.at(chn_num - 1);

    if (static_comp_result.size() < 2)
    {
      LOG_ERROR("channel_num: {} static data not found", chn_num);
      return false;
    }

    comp.code1_dist = std::lround(static_comp_result[0]);
    comp.code2_dist = std::lround(static_comp_result[1]);
  }
  int index = 0;
  static_bit.reg.at(index++).setData(GDI_REG_STATIC_ENABLE, 1);
  static_bit.reg.at(index).setData(GDI_REG_TEMPERATURE, static_cast<uint8_t>(temperature_));

  static_bit.toBigEndian();

  if (!saveFile(_save_path, static_bit.arr.data(), sizeof(static_bit)))
  {
    return false;
  }

  LOG_INFO("生成静标bit文件成功, file path: {}", _save_path.toStdString());
  _static_bit = static_bit;
  return true;
}

// 反标数据压缩，参考方案代码 https://robosense.feishu.cn/docx/ZdjRdkB4yotFuMx8olVcVrT7nCh#WQa2dtzp4oqh4SxwSCLcgBxnnxb
uint16_t compressRefl(const double& _value)
{
  if (_value < 0)
  {
    return 0;
  }

  auto val = std::round(_value / 4);
  // 16383 = 0x3FFF
  if (val > 16383)
  {
    val = 16383;
  }

  if (val < 1024)
  {
    return static_cast<uint16_t>(val);
  }

  if (val >= 1024 && val < 2048)
  {
    return static_cast<uint16_t>(512 * 2 + std::floor(val - 1024) / (1U << 1U));
  }

  if (val >= 2048 && val < 4096)
  {
    return static_cast<uint16_t>(512 * 3 + std::floor(val - 2048) / (1U << 2U));
  }

  if (val >= 4096 && val < 8192)
  {
    return static_cast<uint16_t>(512 * 4 + std::floor(val - 4096) / (1U << 3U));
  }

  if (val >= 8192 && val < 16384)
  {
    return static_cast<uint16_t>(512 * 5 + std::floor(val - 8192) / (1U << 4U));
  }

  return static_cast<uint16_t>(16384 - 1);
}

bool AiryWaveSignalModel::generateReflBit(ReflBit& _refl_bit, const QString& _save_path)
{
  ReflBit refl_bit {};
  std::vector<int> area_charge;
  std::vector<int> amp_charge;

  area_charge = { 0 };
  amp_charge  = { 1 };

  // temp_refl_
  // 求temp_refl_的中位数和均值
  // std::sort(temp_refl_.begin(), temp_refl_.end());
  // auto median = temp_refl_.at(temp_refl_.size() / 2);
  // auto mean   = std::accumulate(temp_refl_.begin(), temp_refl_.end(), 0) / temp_refl_.size();
  // LOG_INFO("temp_refl_ size: {}, median: {}, mean: {}", temp_refl_.size(), median, mean);

  for (auto& channel_num : getChnArray())
  {
    auto& dist_area_map = getDistAreaMap(channel_num);
    for (const auto& charge : area_charge)
    {
      auto& area_comp_chn = refl_bit.comp_chn.at(channel_num - 1).charge.at(charge);
      ReflChargeComp area_comp {};
      for (auto& [refl, area_arr] : dist_area_map.area_comp_fit_result_map)
      {
        for (size_t j = 0; j < area_comp.dist_comp.size(); ++j)
        {
          area_comp.dist_comp.at(j).getReflChargeComp(refl) = compressRefl(area_arr.at(REFL_CALIB_DIST.at(j)));
        }
      }
      area_comp_chn = area_comp;
    }

    for (const auto& charge : amp_charge)
    {
      auto& amp_comp_chn = refl_bit.comp_chn.at(channel_num - 1).charge.at(charge);
      ReflChargeComp amp_comp {};
      for (auto& [refl, amp_arr] : dist_area_map.amp_comp_fit_result_map)
      {
        for (size_t j = 0; j < amp_comp.dist_comp.size(); ++j)
        {
          amp_comp.dist_comp.at(j).getReflChargeComp(refl) = amp_arr.at(REFL_CALIB_DIST.at(j));
        }
      }
      amp_comp_chn = amp_comp;
    }
  }

  for (size_t i = 0; i < REFL_INDEX_VALUE.size(); ++i)
  {
    refl_bit.refl_index_reg.at(i).setData(REF_INDEX_REG_ADDR_START + i, REFL_INDEX_VALUE.at(i));
  }
  for (size_t i = 0; i < DIST_INDEX_VALUE.size(); ++i)
  {
    refl_bit.dist_index_reg.at(i).setData(DIST_INDEX_REG_ADDR_START + (i * 2), DIST_INDEX_VALUE.at(i));
  }

  refl_bit.area_min_index_reg.setData(AREA_MIN_INDEX_REG_ADDR_START, AREA_MIN_INDEX_VALUE);
  refl_bit.peak_min_index_reg.setData(PEAK_MIN_INDEX_REG_ADDR_START, PEAK_MIN_INDEX_VALUE);

  refl_bit.toBigEndian();

  if (!saveFile(_save_path, refl_bit.arr.data(), sizeof(refl_bit)))
  {
    return false;
  }

  LOG_INFO("生成反标bit文件成功, file path: {}", _save_path.toStdString());
  _refl_bit = refl_bit;
  return true;
}

bool AiryWaveSignalModel::generateAbsBit(AbsBit& _abs_bit, const QString& _save_path)
{
  AbsBit abs_bit {};

  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    auto& dist_area_map = getDistAreaMap(chn_num);
    auto& abs_coe_k_vec = dist_area_map.abs_coe_k_vec;
    auto& abs_coe_b_vec = dist_area_map.abs_coe_b_vec;
    auto true_dist_vec  = dist_area_map.dist_true_vec;

    auto index        = chn_num - 1;
    auto& abs_coe_arr = abs_bit.abs_coe_arr.at(index);
    auto& abs_reg     = abs_bit.abs_reg;

    if (true_dist_vec.empty())
    {
      LOG_ERROR("true_dist_vec is empty, chn_num: {}", chn_num);
      return false;
    }

    for (size_t i = true_dist_vec.size(); i < abs_coe_arr.size(); ++i)
    {
      true_dist_vec.push_back(true_dist_vec.back());
    }

    if (abs_coe_k_vec.size() < abs_coe_arr.size())
    {
      LOG_ERROR("abs_coe_k_vec size: {} less than abs_coe_arr size: {}", abs_coe_k_vec.size(), abs_coe_arr.size());
      return false;
    }
    if (true_dist_vec.size() < abs_coe_arr.size())
    {
      LOG_ERROR("true_dist_vec size: {} less than abs_coe_arr size: {}", true_dist_vec.size(), abs_coe_arr.size());
      return false;
    }

    auto abs_addr = GDI_REG_ABS_DIST_START;
    int reg_index = 0;
    for (int i = 0; i < abs_coe_arr.size(); ++i)
    {
      abs_coe_arr.at(i).abs_coe_k = sign2fi(abs_coe_k_vec.at(i), 16, 16);
      abs_coe_arr.at(i).abs_coe_b = sign2fi(abs_coe_b_vec.at(i), 16, 2);

      uint8_t true_val_high8 = static_cast<uint8_t>(static_cast<uint16_t>(std::round(true_dist_vec.at(i))) >> 8U);
      uint8_t true_val_low8  = static_cast<uint8_t>(static_cast<uint16_t>(std::round(true_dist_vec.at(i))) & 0xffU);
      abs_reg.at(reg_index++).setData(abs_addr++, true_val_high8);
      abs_reg.at(reg_index++).setData(abs_addr++, true_val_low8);
    }

    // 最后使能绝标
    abs_reg.at(reg_index++).setData(abs_addr++, 1);
  }
  abs_bit.toBigEndian();

  if (!saveFile(_save_path, abs_bit.arr.data(), sizeof(abs_bit)))
  {
    return false;
  }

  LOG_INFO("生成绝标bit文件成功, file path: {}", _save_path.toStdString());
  _abs_bit = abs_bit;
  return true;
}

bool AiryWaveSignalModel::getEmpAbsBit(EmpAbsBit& _emp_abs_bit)
{
  // QString emp_abs_bit_file_path = app()->getConfigPath() + "B0_newjingyanquxian_sn026.bit";
  // QFile file(emp_abs_bit_file_path);
  // if (!file.open(QIODevice::ReadOnly))
  // {
  //   LOG_ERROR("open file failed, file path: {}", emp_abs_bit_file_path.toStdString());
  //   return false;
  // }
  // if (file.read(_emp_abs_bit.arr.data(), sizeof(_emp_abs_bit)) != sizeof(_emp_abs_bit))
  // {
  //   LOG_ERROR("read file failed, file path: {}", emp_abs_bit_file_path.toStdString());
  //   return false;
  // }

  // LOG_INFO("读取经验绝标bit文件成功, file path: {}", emp_abs_bit_file_path.toStdString());
  return true;
}

bool AiryWaveSignalModel::ifAllBoardNotFound()
{
  bool is_all_10m_board_not_found = true;
  for (const auto& [chn_num, dist_area] : dist_area_map_)
  {
    if (dist_area.board_info_map.at(board_10m_id_).is_found)
    {
      is_all_10m_board_not_found = false;
      break;
    }
  }
  if (is_all_10m_board_not_found)
  {
    LOG_INDEX_ERROR("所有通道筛选出10m板失败，无法生成积分值文件");
    addMeasureMessage("fsm_area_file_generate", false);
    return false;
  }
  return true;
}

std::map<int, AiryWaveSignalModel::AreaState> AiryWaveSignalModel::getAreaStateMap()
{
  std::map<int, AreaState> area_state_map;
  std::map<int, int> refl90_max_map;
  for (int i = 1; i < total_chn_num_; i += 4)
  {
    // 提取当前四个通道的 refl90_area 值
    std::vector<int> refl90_area_vec(4);
    for (int j = 0; j < 4; ++j)
    {
      auto area_mean     = getDistAreaMap(i + j).getReflBoard(board_10m_id_, 90).area_mean;
      refl90_area_vec[j] = roundToInt(area_mean);
    }

    // 计算最大值和最小值的索引
    // NOLINTNEXTLINE
    int max_value = *std::max_element(refl90_area_vec.begin(), refl90_area_vec.end());
    // NOLINTNEXTLINE
    int min_element_index =
      // NOLINTNEXTLINE
      std::distance(refl90_area_vec.begin(), std::min_element(refl90_area_vec.begin(), refl90_area_vec.end()));

    // 填充映射表，并设置标志位
    for (int j = 0; j < 4; ++j)
    {
      int channel                  = i + j;
      auto dist_mean               = getDistAreaMap(channel).getReflBoard(board_10m_id_, 90).dist_mean;
      area_state_map[channel].dist = roundToInt(dist_mean);
      area_state_map[channel].area = refl90_area_vec[j];
      refl90_max_map[channel]      = max_value;

      getDistAreaMap(channel).refl90_is_min_area = (j == min_element_index);
    }
  }

  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    int area                     = area_state_map[chn_num].area;
    float rate                   = static_cast<float>(area) / static_cast<float>(refl90_max_map[chn_num]);
    area_state_map[chn_num].rate = rate;
  }
  return area_state_map;
}

AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkAreaType(
  std::map<int, AiryWaveSignalModel::AreaState>& _area_state_map,
  tsl::ordered_map<mech::MountType, AreaType>& _mount_area_result)
{
  if (mount_type_ == mech::MountType::MOW)
  {
    auto area_type = checkMowAreaType(_area_state_map);
    _mount_area_result.insert({ mount_type_, area_type });
    return area_type;
  }

  AreaType area_type = AreaType::UNKNOWN;
  switch (mount_type_)
  {
  case mech::MountType::FRONT:
  {
    area_type = checkFrontAreaType(_area_state_map);
    break;
  }
  case mech::MountType::SIDE:
  {
    area_type = checkSideAreaType(_area_state_map);
    break;
  }
  case mech::MountType::MAPPING:
  {
    area_type = checkMappingAreaType(_area_state_map);
    break;
  }
  default:
  {
    app()->signalShowErrorText("无法识别安装方式");
    area_type = checkFrontAreaType(_area_state_map);
  }
  }
  _mount_area_result.insert({ mount_type_, area_type });
  if (area_type == AreaType::NG)
  {
    auto sub_area_type = checkMowAreaType(_area_state_map);
    _mount_area_result.insert({ mech::MountType::MOW, sub_area_type });
    updateIfMowOK(sub_area_type == AreaType::PASS);
  }

  return area_type;
}
AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkFrontAreaType(std::map<int, AreaState>& _area_state_map)
{
  // CH69~96 积分值标准按60%；CH1~68 积分值标准按65%；NG通道在CH69、CH80~96，CH4，CH8，CH12，通道数不超过5个
  AreaTypeLimit area_type_limit;
  area_type_limit.mount_type      = mech::MountType::FRONT;
  area_type_limit.area_ng_max_num = 5;
  auto& area_th_min               = area_type_limit.area_th_min;
  auto& area_allow_chn            = area_type_limit.area_allow_chn;
  for (int i = 1; i < 69; ++i)
  {
    area_th_min[i] = 0.585;
  }
  for (int i = 69; i <= 96; ++i)
  {
    area_th_min[i] = 0.54;
  }
  area_allow_chn = { 4, 8, 12, 69 };
  for (int i = 80; i <= 96; ++i)
  {
    area_allow_chn.insert(i);
  }

  return checkAreaTypeLimit(area_type_limit, _area_state_map);
}

AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkSideAreaType(std::map<int, AreaState>& _area_state_map)
{
  // CH1~CH24 积分值标准按60%；CH25~CH96 积分值标准按65%；NG通道在CH1~CH8、CH69、CH89、CH93，通道数不超过5个
  AreaTypeLimit area_type_limit;
  area_type_limit.mount_type      = mech::MountType::SIDE;
  area_type_limit.area_ng_max_num = 5;
  auto& area_th_min               = area_type_limit.area_th_min;
  auto& area_allow_chn            = area_type_limit.area_allow_chn;
  for (int i = 1; i <= 24; ++i)
  {
    area_th_min[i] = 0.54;
  }
  for (int i = 25; i <= 96; ++i)
  {
    area_th_min[i] = 0.585;
  }
  area_allow_chn = { 69, 89, 93 };
  for (int i = 1; i <= 8; ++i)
  {
    area_allow_chn.insert(i);
  }

  // 判断积分值ng的通道数
  return checkAreaTypeLimit(area_type_limit, _area_state_map);
}
AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkMappingAreaType(std::map<int, AreaState>& _area_state_map)
{
  // "CH1~CH16，CH85~CH96不管控，其余4倍数通道（CH20/CH24...）不管控
  // 剩余通道按积分值70 %
  // 进行管控，NG通道不超过2个，且NG通道积分值不低于60 % "
  AreaTypeLimit area_type_limit;
  area_type_limit.mount_type       = mech::MountType::MAPPING;
  area_type_limit.area_ng_max_num  = 2;
  area_type_limit.area_ng_min_rate = 0.6;
  auto& area_th_min                = area_type_limit.area_th_min;
  auto& area_allow_chn             = area_type_limit.area_allow_chn;
  for (int i = 17; i < 85; ++i)
  {
    area_th_min[i] = 0.7;
  }
  for (int i = 1; i <= 16; ++i)
  {
    area_th_min[i] = -1;
  }
  for (int i = 85; i <= 96; ++i)
  {
    area_th_min[i] = -1;
  }
  // 4倍数通道不管控
  for (int i = 16; i < 85; i += 4)
  {
    area_th_min[i] = -1;
  }
  for (int i = 16; i < 85; i += 1)
  {
    area_allow_chn.insert(i);
  }

  return checkAreaTypeLimit(area_type_limit, _area_state_map);
}

AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkMowAreaType(std::map<int, AreaState>& _area_state_map)
{
  // CH1 ~48 积分值标准按58.5 %
  // 同时不管控CH49~CH96通道
  AreaTypeLimit area_type_limit;
  area_type_limit.mount_type      = mech::MountType::MOW;
  area_type_limit.area_ng_max_num = 3;
  auto& area_th_min               = area_type_limit.area_th_min;
  auto& area_allow_chn            = area_type_limit.area_allow_chn;
  for (int i = 1; i <= 48; ++i)
  {
    area_th_min[i] = 0.585;
  }
  area_allow_chn = { 4, 8, 12 };
  for (int i = 49; i <= total_chn_num_; ++i)
  {
    area_th_min[i] = -1;
  }
  return checkAreaTypeLimit(area_type_limit, _area_state_map);
}
AiryWaveSignalModel::AreaType AiryWaveSignalModel::checkAreaTypeLimit(const AreaTypeLimit& _area_type_limit,
                                                                      std::map<int, AreaState>& _area_state_map)
{
  const auto& area_th_min      = _area_type_limit.area_th_min;
  const auto& area_ng_max_num  = _area_type_limit.area_ng_max_num;
  const auto& area_ng_min_rate = _area_type_limit.area_ng_min_rate;
  const auto& area_allow_chn   = _area_type_limit.area_allow_chn;

  auto mount_type_str = QString::fromStdString(fmt::format("{}", _area_type_limit.mount_type)).toLower();

  if (area_th_min.size() != _area_state_map.size())
  {
    LOG_INDEX_ERROR("积分值通道数与阈值通道数不一致, area_th_min.size(): {}, _area_state_map.size(): {}",
                    area_th_min.size(), _area_state_map.size());
    return AreaType::ABNORMAL;
  }

  float area_min = 1;
  std::vector<int> area_ng_chn;
  // 判断积分值ng的通道数
  for (auto& [channel_num, area_state] : _area_state_map)
  {
    auto limit_info   = limit_csv_utils_ptr_->getLimitInfo("area");
    limit_info.min_th = area_th_min.at(channel_num);
    limit_info.setNameSuffix(fmt::format("_{}_chn_{}", mount_type_str, channel_num));

    area_state.limit_min_vec.emplace_back(limit_info.min_th);
    area_state.result_vec.emplace_back(
      addMeasureMessage(limit_info, area_state.rate, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT));
    if (!area_state.result_vec.back())
    {
      area_ng_chn.push_back(channel_num);
    }

    if (area_state.rate < 0.001)
    {
      zero_area_chn_vec_.push_back(channel_num);
    }
    if (limit_info.min_th < 0)
    {
      continue;
    }
    area_min = std::min(area_min, area_state.rate);
  }

  // 判断积分值是否存在0
  if (!zero_area_chn_vec_.empty())
  {
    std::string err_str = fmt::format("通道{}积分值最小值为0，积分值判断异常", zero_area_chn_vec_);
    LOG_INDEX_ERROR(err_str);
    return AreaType::ABNORMAL;
  }

  auto area_type              = AreaType::PASS;
  auto limit_info_area_ng_max = limit_csv_utils_ptr_->getLimitInfo("area");
  limit_info_area_ng_max.setNameSuffix(fmt::format("_{}_ng_max_num", mount_type_str));
  limit_info_area_ng_max.min_th               = 0;
  limit_info_area_ng_max.max_th               = area_ng_max_num;
  limit_info_area_ng_max.extra_str_info.at(0) = "积分值最大允许NG数";
  if (!addMeasureMessage(limit_info_area_ng_max, static_cast<int>(area_ng_chn.size()),
                         rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    LOG_INDEX_ERROR("积分值通道数: {}，阈值超限，上限为: {}", area_ng_chn.size(), limit_info_area_ng_max.max_th);
    area_type = AreaType::NG;
  }

  // 判断ng通道是否在允许范围内
  std::vector<int> not_allow_chn;
  for (auto channel_num : area_ng_chn)
  {
    if (area_allow_chn.count(channel_num) != 0U)
    {
      continue;
    }

    not_allow_chn.push_back(channel_num);
    auto limit_info_not_allow_chn = limit_csv_utils_ptr_->getLimitInfo("area");
    limit_info_not_allow_chn.setNameSuffix(fmt::format("_{}_ng_not_allow_chn_{}", mount_type_str, channel_num));
    limit_info_not_allow_chn.min_th               = 0;
    limit_info_not_allow_chn.max_th               = 0;
    limit_info_not_allow_chn.extra_str_info.at(0) = "积分值不允许NG通道";
    addMeasureMessage(limit_info_not_allow_chn, channel_num, rsfsc_lib::MEASURE_DATA_TYPE_INT);
  }

  if (!not_allow_chn.empty())
  {
    LOG_INDEX_ERROR("积分值通道{}不在允许范围内", not_allow_chn);
    area_type = AreaType::NG;
  }

  auto limit_info_area_min = limit_csv_utils_ptr_->getLimitInfo("area");
  limit_info_area_min.setNameSuffix(fmt::format("_{}_min", mount_type_str));
  limit_info_area_min.extra_str_info.at(0) = "积分值最小值";
  limit_info_area_min.min_th               = area_ng_min_rate;
  limit_info_area_min.max_th               = 1.0001;
  if (!addMeasureMessage(limit_info_area_min, area_min, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    LOG_INDEX_ERROR("积分值最小值: {:.3f}，小于最小阈值: {:.3f}", area_min, limit_info_area_min.min_th);
    area_type = AreaType::NG;
  }

  return area_type;
}
bool AiryWaveSignalModel::saveAreaData(std::map<int, AiryWaveSignalModel::AreaState>& _area_state_map,
                                       tsl::ordered_map<mech::MountType, AreaType>& _mount_area_result)
{
  std::string result;
  for (const auto& item : _mount_area_result)
  {
    result += "_";
    result += fmt::format("{}_{}", item.first, item.second);
  }
  path_.refl90_10m_file_name   = fmt::format(path_.refl90_10m_file_name.toStdString(), result).c_str();
  QString refl90_10m_file_path = path_.data_dir.absoluteFilePath(path_.refl90_10m_file_name);
  QFile file(refl90_10m_file_path);
  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    const auto& area_state = _area_state_map[chn_num];
    if (area_state.limit_min_vec.size() != area_state.result_vec.size())
    {
      LOG_ERROR("积分值通道数与阈值通道数不一致, channel_num: {}, limit_min_vec.size(): {}, "
                "result_vec.size(): {}",
                chn_num, area_state.limit_min_vec.size(), area_state.result_vec.size());
      return false;
    }
  }
  if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate | QIODevice::Text))
  {
    LOG_ERROR("open file failed, file path: {}, err_msg: {}", refl90_10m_file_path, file.errorString());
    addMeasureMessage("fsm_area_file_generate", false);
    return false;
  }
  // file.write("\xEF\xBB\xBF");
  QTextStream out(&file);
  out.setCodec("UTF-8");  // Qt 5 的写法
  out.setGenerateByteOrderMark(true);
  out << QString("通道号,测距(0.5cm),回波面积,积分值比例");
  for (const auto& item : _mount_area_result)
  {
    out << QString(",");
    out << QString::fromStdString(fmt::format("{}标准下限,结果{}", item.first, item.second));
  }
  out << endl;
  for (int chn_num = 1; chn_num <= total_chn_num_; ++chn_num)
  {
    const auto& area_state = _area_state_map[chn_num];
    int dist               = area_state.dist;
    int area               = area_state.area;
    float rate             = area_state.rate;
    out << fmt::format("{},{},{},{:.3f}", chn_num, dist, area, rate).c_str();
    for (int i = 0; i < area_state.result_vec.size(); ++i)
    {
      out << ",";
      float threshold_min = area_state.limit_min_vec[i];
      std::string pass    = area_state.result_vec[i] ? "" : "NG";
      out << fmt::format("{:.3f},{}", threshold_min, pass).c_str();
    }
    out << endl;
  }
  file.close();
  addMeasureMessage("fsm_area_file_generate", true);
  return true;
}

bool AiryWaveSignalModel::save90Refl10m()
{
  if (!ifAllBoardNotFound())
  {
    return false;
  }

  auto area_state_map = getAreaStateMap();
  tsl::ordered_map<mech::MountType, AreaType> mount_area_result;

  product_area_type_ = checkAreaType(area_state_map, mount_area_result);

  if (!saveAreaData(area_state_map, mount_area_result))
  {
    return false;
  }

  updateAreaType(product_area_type_);
  if (product_area_type_ == AreaType::PASS)
  {
    LOG_INDEX_INFO("积分值判定为: {}", product_area_type_);
  }
  else
  {
    LOG_INDEX_ERROR("积分值判定为: {}", product_area_type_);
  }
  return product_area_type_ == AreaType::PASS;
}

bool AiryWaveSignalModel::writeBitToLidar(const QString& _bit_file_path)
{
  QString file_path;
  if (_bit_file_path.isEmpty())
  {
    if (combine_bit_file_path_.isEmpty())
    {
      LOG_ERROR("combine bit file path is empty");
      return false;
    }
    file_path = combine_bit_file_path_;
  }
  else
  {
    file_path = _bit_file_path;
  }
  if (!getLidarManager()->writeTopFlash(file_path, 0xF00000, 0xFFFFFF))
  {
    LOG_ERROR("write bit to lidar failed, file path: {}", file_path);
    return false;
  }

  return true;
}

bool AiryWaveSignalModel::setPcapFilePath(const QString& _pcap_file_path)
{
  if (_pcap_file_path.isEmpty())
  {
    LOG_ERROR("pcap file path is empty");
    return false;
  }

  initPath(_pcap_file_path);

  return true;
}

// template void AiryWaveSignalModel::writeVector<int>(QTextStream& _out,
//                                                     const std::vector<int>& _vec,
//                                                     const size_t _index);
// template void AiryWaveSignalModel::writeVector<float>(QTextStream& _out,
//                                                       const std::vector<float>& _vec,
//                                                       const size_t _index);

template <typename T>
void writeVector(QTextStream& _out, const std::vector<T>& _vec, const size_t _index)
{
  if (_index < _vec.size())
  {
    _out << _vec[_index];
  }
  _out << ",";
}
void writeVector(QTextStream& _out, const std::vector<DynamicComp>& _vec, const size_t _index)
{
  if (_index < _vec.size())
  {
    _out << _vec[_index].dist_val_arr.at(static_cast<size_t>(_index));
  }
  _out << ",";
}

bool AiryWaveSignalModel::saveZero(const bool _is_test)
{
  QString name      = _is_test ? "zero_test_info.json" : "zero_info.json";
  QString file_path = path_.process_data_dir.absoluteFilePath(name);
  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("open file failed, file path: {}", file_path.toStdString());
    return false;
  }
  QTextStream out(&file);

  QJsonObject json_obj;

  QJsonArray zero_data_azi;
  QJsonArray zero_data_dist;
  QJsonArray zero_data_area;

  json_obj["zero_azi_start"]      = zero_azi_start_;
  json_obj["zero_azi_end"]        = zero_azi_end_;
  json_obj["zero_hor_chn1_angle"] = zero_hor_chn1_angle_;
  json_obj["zero_high_refl_min"]  = zero_high_refl_min_;
  if (_is_test)
  {
    json_obj["zero_angle"] = zero_test_angle_;
    for (const auto& [dist, area, azi] : zero_test_data_)
    {
      zero_data_azi.append(azi);
      zero_data_dist.append(dist);
      zero_data_area.append(area);
    }
    json_obj["zero_data_azi"]  = zero_data_azi;
    json_obj["zero_data_dist"] = zero_data_dist;
    json_obj["zero_data_area"] = zero_data_area;

    out << QJsonDocument(json_obj).toJson(QJsonDocument::Indented);
    file.close();
    LOG_INFO("save zero info success, file path: {}", file_path.toStdString());
    return true;
  }

  json_obj["zero_angle"] = zero_angle_;
  for (const auto& [dist, area, azi] : zero_data_)
  {
    zero_data_azi.append(static_cast<int>(azi));
    zero_data_dist.append(static_cast<int>(dist));
    zero_data_area.append(static_cast<int>(area));
  }
  json_obj["zero_data_azi"]  = zero_data_azi;
  json_obj["zero_data_dist"] = zero_data_dist;
  json_obj["zero_data_area"] = zero_data_area;

  out << QJsonDocument(json_obj).toJson(QJsonDocument::Indented);
  LOG_INFO("save zero info success, file path: {}", file_path.toStdString());
  app()->signalUpdateProcessDataPath(getDataPath());
  file.close();
  return true;
}

bool AiryWaveSignalModel::saveDistAreaMap(const int _channel_num)
{
  QString file_path = path_.process_data_dir.absoluteFilePath(fmt::format("chn{}_dist_area.csv", _channel_num).c_str());
  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("open file failed, file path: {}", file_path.toStdString());
    return false;
  }
  QTextStream out(&file);
  auto& data           = getDistAreaMap(_channel_num);
  auto& board_info_map = data.board_info_map;

  size_t max_length = 0;
  max_length        = std::max(max_length, data.dist_vec.size());
  max_length        = std::max(max_length, data.dynamic_comp_result.at(data.dynamic_code_mark).size());

  // Iterate through each index up to max_length
  out << "dist_vec,"
      << "area_vec,"
      << "amp_vec,"
      << "code_mark_vec,"
      << "dist_comped_vec,"
      << "dynamic_comp_result_0,dynamic_comp_result_1,";
  // << "dynamic_comp_result,";
  for (const auto& [refl, vec] : data.area_comp_fit_result_map)
  {
    out << fmt::format("area_comp_{}", refl).c_str() << ",";
  }
  for (const auto& [refl, vec] : data.amp_comp_fit_result_map)
  {
    out << fmt::format("amp_comp_{}", refl).c_str() << ",";
  }
  out << "\n";

  for (size_t i = 0; i < max_length; ++i)
  {
    writeVector(out, data.dist_vec, i);
    writeVector(out, data.area_vec, i);
    writeVector(out, data.amp_vec, i);
    writeVector(out, data.code_mark_vec, i);
    writeVector(out, data.dist_comped_vec, i);
    writeVector(out, data.dynamic_comp_result.at(0), i);
    writeVector(out, data.dynamic_comp_result.at(1), i);
    // writeVector(out, combine_bit_.dynamic_bit.comp.at(_channel_num), i);

    for (const auto& [refl, vec] : data.area_comp_fit_result_map)
    {
      writeVector(out, vec, i);
    }
    for (const auto& [refl, vec] : data.amp_comp_fit_result_map)
    {
      writeVector(out, vec, i);
    }
    out << "\n";
  }

  file.close();
  return true;
}

bool AiryWaveSignalModel::saveInfo(const int _channel_num)
{
  ScopedTimer timer(fmt::format("保存靶板: {} ", _channel_num).c_str());
  auto& dist_area_map  = getDistAreaMap(_channel_num);
  auto& board_info_map = dist_area_map.board_info_map_bak;

  QString file_path = path_.process_data_dir.absoluteFilePath(fmt::format("chn{}_info.json", _channel_num).c_str());
  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("open file failed, file path: {}", file_path.toStdString());
    return false;
  }
  QTextStream out(&file);

  QJsonObject json_obj;

  QJsonObject board;
  for (const auto& [board_id, board_info] : board_info_map)
  {
    QJsonObject board_info_json;
    board_info_json["board_id"] = board_id;
    // board_info_json["board_index_min"]     = board_info.board_index_min;
    // board_info_json["board_index_max"]     = board_info.board_index_max;
    board_info_json["raw_dist_min"]        = board_info.raw_dist_min;
    board_info_json["raw_dist_max"]        = board_info.raw_dist_max;
    board_info_json["detected_data_start"] = board_info.detected_data_start;
    board_info_json["detected_data_end"]   = board_info.detected_data_end;
    board_info_json["distance_05cm"]       = board_info.distance_05cm;
    board_info_json["board_start_angle"]   = board_info.board_start_angle;
    board_info_json["board_end_angle"]     = board_info.board_end_angle;
    board_info_json["board_index_length"]  = board_info.board_index_length;
    board_info_json["is_found"]            = static_cast<int>(board_info.is_found);
    QJsonObject refl_board_json;
    for (const auto& board_pos : board_info.refl_board_vec)
    {
      auto refl = board_pos.refl;
      QJsonObject refl_json;
      refl_json["pos_factor"]                = board_pos.pos_factor;
      refl_json["area_index"]                = board_pos.area_index;
      refl_json["amp_index"]                 = board_pos.amp_index;
      refl_json["distance_05cm"]             = board_pos.distance_05cm;
      refl_json["area_mean"]                 = board_pos.area_mean;
      refl_json["area_mean_code_0"]          = board_pos.area_mean_code_0;
      refl_json["area_mean_code_1"]          = board_pos.area_mean_code_1;
      refl_json["amp_mean"]                  = board_pos.amp_mean;
      refl_json["amp_mean_code_0"]           = board_pos.amp_mean_code_0;
      refl_json["amp_mean_code_1"]           = board_pos.amp_mean_code_1;
      refl_json["dist_mean"]                 = board_pos.dist_mean;
      refl_board_json[QString::number(refl)] = refl_json;
    }
    board_info_json["refl_board"] = refl_board_json;

    board[QString::number(board_id)] = board_info_json;
  }
  json_obj["board"] = board;

  QJsonArray static_comp_array;
  QJsonArray abs_board_id_vec;
  QJsonArray refl_board_id_vec;
  QJsonArray dist_true_vec;
  QJsonArray dist_test_vec;
  QJsonArray dist_error_vec;
  QJsonArray abs_coe_k_vec;
  QJsonArray abs_coe_b_vec;
  for (const auto& item : dist_area_map.static_comp_result)
  {
    static_comp_array.append(item);
  }
  for (const auto& item : abs_board_id_vec_)
  {
    abs_board_id_vec.append(item);
  }
  for (const auto& item : refl_board_id_vec_)
  {
    refl_board_id_vec.append(item);
  }
  for (const auto& item : dist_area_map.dist_true_vec)
  {
    dist_true_vec.append(item);
  }
  for (const auto& item : dist_area_map.dist_test_vec)
  {
    dist_test_vec.append(item);
  }
  for (const auto& item : dist_area_map.dist_error_vec)
  {
    dist_error_vec.append(item);
  }
  for (const auto& item : dist_area_map.abs_coe_k_vec)
  {
    abs_coe_k_vec.append(item);
  }
  for (const auto& item : dist_area_map.abs_coe_b_vec)
  {
    abs_coe_b_vec.append(item);
  }

  json_obj["static_comp"]         = static_comp_array;
  json_obj["abs_board_id_vec"]    = abs_board_id_vec;
  json_obj["refl_board_id_vec"]   = refl_board_id_vec;
  json_obj["dist_true_vec"]       = dist_true_vec;
  json_obj["dist_test_vec"]       = dist_test_vec;
  json_obj["dist_error_vec"]      = dist_error_vec;
  json_obj["abs_coe_k_vec"]       = abs_coe_k_vec;
  json_obj["abs_coe_b_vec"]       = abs_coe_b_vec;
  json_obj["dynamic_board_id"]    = dynamic_board_id_;
  json_obj["static_board_id"]     = static_board_id_;
  json_obj["dynamic_start_index"] = dist_area_map.dynamic_start_index;
  json_obj["dynamic_code_mark"]   = dist_area_map.dynamic_code_mark;
  json_obj["dynamic_min_dist"]    = dist_area_map.dynamic_min_dist;
  json_obj["board_10m_id"]        = board_10m_id_;
  json_obj["board_20m_id"]        = board_20m_id_;

  out << QJsonDocument(json_obj).toJson().toStdString().c_str();

  file.close();
  return true;
}

bool AiryWaveSignalModel::saveAllProcessData()
{
  ScopedTimer timer("保存数据");
  int total_task = 2 * total_chn_num_;
  app()->signalUpdateProgressSaveProcessDataTotalTask(total_task);
  int progress = 0;
  for (int i = 1; i <= total_chn_num_; ++i)
  {
    if (!saveDistAreaMap(i))
    {
      return false;
    }
    app()->signalUpdateProgressSaveProcessData(progress++);
    if (!saveInfo(i))
    {
      return false;
    }
    app()->signalUpdateProgressSaveProcessData(progress++);
  }
  app()->signalUpdateProgressSaveProcessData(total_task);
  LOG_INFO("保存成功: {}", path_.process_data_dir.absolutePath());

  app()->signalUpdateProcessDataPath(getDataPath());
  return true;
}

bool AiryWaveSignalModel::showData(const int _channel_num,
                                   const int _process_num,
                                   QString _type,
                                   QString _data_path,
                                   const bool _is_show,
                                   const bool _is_save) const
{
  ScopedTimer timer("show data");
  if (_data_path.isEmpty())
  {
    _data_path = path_.data_dir.absolutePath();
  }
  QDir process_data_dir(_data_path);
  if (!process_data_dir.exists())
  {
    LOG_ERROR("process data dir not exists, dir: {}", _data_path);
    return false;
  }
  if (process_data_dir.entryList(QDir::Files).isEmpty())
  {
    LOG_ERROR("process data dir is empty, dir: {}", _data_path);
    return false;
  }

  std::vector<int> show_chn_vec;
  QString draw_figure_script_path = app()->getScriptPath() + "draw_figure.py";

  int total_task = 1;
  int progress   = 0;

  if (_channel_num == 0)
  {
    std::vector<int> chn_vec;
    for (int i = 1; i <= total_chn_num_; ++i)
    {
      chn_vec.push_back(i);
    }
    show_chn_vec = std::vector<int>(chn_vec.begin(), chn_vec.end());
    total_task   = static_cast<int>(show_chn_vec.size());
  }
  else
  {
    show_chn_vec = { _channel_num };
  }

  app()->signalUpdateProgressGenerateFigureTotalTask(total_task);
  app()->signalUpdateProgressGenerateFigure(progress);
  int thread_size = std::min(_process_num, static_cast<int>(show_chn_vec.size()));

  ThreadPool::getInstance();
  ThreadPool thread_pool(thread_size);

  std::vector<std::shared_ptr<QProcess>> processes;
  std::vector<std::shared_ptr<PyProcessTask>> tasks;

  for (const auto& chn_num : show_chn_vec)
  {
    QStringList arguments;
    arguments << draw_figure_script_path << _data_path << QString::number(chn_num) << _type;
    if (_is_save)
    {
      arguments << "--save";
    }
    if (_is_show)
    {
      arguments << "--show";
    }

    QString info = fmt::format("{}通道, 类型: {}", chn_num, _type).c_str();
    auto task    = std::make_shared<PyProcessTask>("python3", arguments, info);
    tasks.push_back(task);
    thread_pool.addTask(task);
  }

  bool is_success = true;
  for (auto& task : tasks)
  {
    bool res = task->getResult();
    if (!res)
    {
      LOG_ERROR("show data failed");
      is_success = false;
    }
    app()->signalUpdateProgressGenerateFigure(++progress);
  }

  app()->signalUpdateProgressGenerateFigure(total_task);
  LOG_INFO("生成结束");
  return is_success;
}

bool AiryWaveSignalModel::showZeroData(QString _data_path, const bool _is_show, const bool _is_save) const
{
  // 判断路径是否合法
  QDir data_dir(_data_path);
  if (_data_path.isEmpty())
  {
    _data_path = path_.data_dir.absolutePath();
  }
  QDir process_data_dir(_data_path);
  if (!process_data_dir.exists())
  {
    LOG_ERROR("process data dir not exists, dir: {}", _data_path);
    return false;
  }

  QStringList arguments;
  QString draw_figure_script_path = app()->getScriptPath() + "draw_zero_figure.py";
  arguments << draw_figure_script_path << _data_path;
  if (_is_save)
  {
    arguments << "--save";
  }
  if (_is_show)
  {
    arguments << "--show";
  }

  auto task = std::make_shared<PyProcessTask>("python3", arguments, "zero");
  ThreadPool thread_pool(1);
  thread_pool.addTask(task);
  bool res = task->getResult();
  if (!res)
  {
    LOG_ERROR("show zero data failed");
    return false;
  }
  return true;
}

void AiryWaveSignalModel::updateAreaType(const AreaType _area_type)
{
  product_area_type_ = _area_type;
  app()->signalUpdateProductDisplay(fmt::format("积分值: {}", _area_type).c_str());
  if (_area_type == AreaType::UNKNOWN)
  {
    clearMowType();
  }
}
void AiryWaveSignalModel::updateIfMowOK(const bool _is_mow_ok)
{
  app()->signalUpdateMowAreaType(QString("割草版: ") + (_is_mow_ok ? QString("适合") : QString("不适合")));
}
void AiryWaveSignalModel::clearMowType() { app()->signalUpdateMowAreaType(""); }

void PyProcessTask::run()
{
  // LOG_INFO("Process command: python3 {}", arguments_.join(" ").toStdString());
  QProcess process;
  process.setProgram("python3");
  process.setArguments(arguments_);
  process.start();
  process.waitForFinished(-1);
  // LOG_INFO("Process command: python3 finish");

  if (process.exitCode() != 0)
  {
    LOG_ERROR("Process error output: {}", process.readAllStandardError().toStdString());
    LOG_ERROR("Process failed: {}", arguments_.join(" ").toStdString());
    LOG_ERROR("生成图片失败: {}", info_);
  }
  else
  {
    LOG_INFO("生成完成: {}", info_);
  }

  setResult(process.exitCode() == 0);
}

}  // namespace lidar
}  // namespace robosense
