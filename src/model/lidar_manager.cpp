﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "model/lidar_manager.h"
#include "app_event.h"
#include "mech_communication/mech_communication.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "mech_udp.h"
#include "model/data_struct.h"
#include "rs_logger.h"
#include "rsfsc_utils/tcpdump_utils.h"
#include "utils/crc_utils.h"
#include <QFile>
#include <QHostAddress>
#include <cstdint>
#include <qnamespace.h>
#include <thread>
#include <unistd.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

LidarManager::LidarManager(WidgetLidarInfo* _lidar_info) :
  lidar_info_(_lidar_info),
  mech_communication_ptr_(std::make_shared<MechCommunication>(lidar_info_->getProjectCodeStr().toStdString()))
{
  loadConfigData();
}

void LidarManager::loadConfigData()
{
  reg_csv_utils_ptr_      = app()->getCsvUtils("airy_reg");
  angle_48_csv_utils_ptr_ = app()->getCsvUtils("airy_angle_48");
  angle_96_csv_utils_ptr_ = app()->getCsvUtils("airy_angle_96");

  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_ERROR("获取reg csv解析器失败");
    return;
  }

  if (angle_48_csv_utils_ptr_ == nullptr || angle_96_csv_utils_ptr_ == nullptr)
  {
    LOG_ERROR("获取angle csv解析器失败");
    return;
  }

  if (!angle_48_csv_utils_ptr_->getAngleData(angle_vec_vec_48_) ||
      !angle_96_csv_utils_ptr_->getAngleData(angle_vec_vec_96_))
  {
    LOG_ERROR("获取角度信息失败");
    return;
  }
  // LOG_INFO("angle_vec_vec : {}", angle_vec_vec);

  auto reg_info_map = reg_csv_utils_ptr_->getAllRegisterInfo();
  if (reg_info_map.empty())
  {
    LOG_ERROR("获取寄存器信息失败");
  }
}
void LidarManager::setLidarInfo(WidgetLidarInfo* _lidar_info) { lidar_info_ = _lidar_info; }
WidgetLidarInfo* LidarManager::getLidarInfo() const { return lidar_info_; };

bool LidarManager::ping() { return mech_communication_ptr_->ping(lidar_info_->getIP().toStdString()); }

void LidarManager::setLidarInfoIP(const QString& _ip)
{
  // QMetaObject::invokeMethod(lidar_info_, "setIP", Qt::BlockingQueuedConnection, Q_ARG(QString, _ip));
  lidar_info_->setIP(_ip);
}
void LidarManager::setLidarInfoMSOP(const quint16 _msop)
{
  // QMetaObject::invokeMethod(lidar_info_, "setMSOP", Qt::BlockingQueuedConnection, Q_ARG(quint16, _msop));
  lidar_info_->setMSOP(_msop);
}
void LidarManager::setLidarInfoDIFOP(const quint16 _difop)
{
  // QMetaObject::invokeMethod(lidar_info_, "setDIFOP", Qt::BlockingQueuedConnection, Q_ARG(quint16, _difop));
  lidar_info_->setDIFOP(_difop);
}

bool LidarManager::scanFirstLidarAndSetIP()
{
  if (auto ip_port = TcpdumpUtils().captureOneAiryNetInfo())
  {
    setLidarInfoIP(QString::fromStdString(ip_port->ip));
    setLidarInfoMSOP(ip_port->msop_port);
    setLidarInfoDIFOP(ip_port->difop_port);
    return true;
  }
  return false;
}

bool LidarManager::waitForTop() { return mech_communication_ptr_->waitForTopStartUp(30000); }

bool LidarManager::connect()
{
  auto ip_addr = lidar_info_->getIP();
  auto port    = lidar_info_->getMSOPPort();
  mech_communication_ptr_->setLogIndex(lidar_info_->getLidarIndex());
  if (ip_addr.isEmpty() || port == 0)
  {
    LOG_ERROR("雷达-{} 连接失败, error_msg: IP不能为空", lidar_info_->getLidarIndex());
    return false;
  }

  AppEvent::getInstance()->signalLidarConnecting(lidar_info_->getLidarIndex());
  try
  {
    if (!mech_communication_ptr_->connect(ip_addr.toStdString(), port, 30000))
    {
      LOG_ERROR("雷达-{} 连接失败 {}:{}", lidar_info_->getLidarIndex(), lidar_info_->getIP().toStdString(),
                lidar_info_->getMSOPPort());
      AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
      return false;
    }
    LOG_INFO("雷达-{} 连接成功 {}:{}", lidar_info_->getLidarIndex(), lidar_info_->getIP().toStdString(),
             lidar_info_->getMSOPPort());
  }
  catch (const std::exception& e)
  {
    LOG_ERROR("雷达-{} 连接异常 {}:{}, {}", lidar_info_->getLidarIndex(), lidar_info_->getIP().toStdString(),
              lidar_info_->getMSOPPort(), e.what());
    AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
    return false;
  }

  LOG_INDEX_INFO("正等待顶板启动完成...");
  if (!mech_communication_ptr_->waitForTopStartUp(35000))
  {
    LOG_INDEX_ERROR("等待雷达MSOP超时");
    AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
    mech_communication_ptr_->disconnect();
    return false;
  }

  if (!autoSetLidarSN())
  {
    LOG_ERROR("自动设置雷达-{}SN失败", lidar_info_->getLidarIndex());
    AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
    mech_communication_ptr_->disconnect();
    return false;
  }

  MechUdp mech_udp(sizeof(difop_packet_));
  if (auto difop_packet = mech_udp.getOneDifopPacket(ip_addr.toStdString(), lidar_info_->getDIFOPPort(), 10))
  {
    difop_packet_ = *difop_packet;
  }
  else
  {
    LOG_ERROR("雷达-{} 获取difop失败 {}:{}, difop: {}", lidar_info_->getLidarIndex(),
              lidar_info_->getIP().toStdString(), lidar_info_->getMSOPPort(), lidar_info_->getDIFOPPort());
    mech_communication_ptr_->disconnect();
    AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
    return false;
  }

  AppEvent::getInstance()->signalLidarConnected(lidar_info_->getLidarIndex());
  return true;
}

bool LidarManager::disconnect()
{
  try
  {
    AppEvent::getInstance()->signalLidarDisconnecting(lidar_info_->getLidarIndex());
    mech_communication_ptr_->disconnect();
  }
  catch (const std::exception& e)
  {
    RSFSCLog::getInstance()->error("LidarManager::disconnect: 雷达断开连接异常, {}", e.what());
    AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
    return false;
  }
  AppEvent::getInstance()->signalLidarDisconnected(lidar_info_->getLidarIndex());
  return true;
}

bool LidarManager::setZeroAngle(float _angle)
{
  if (!mech_communication_ptr_->writeCmd(0x6006, _angle))
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    if (!mech_communication_ptr_->writeCmd(0x6006, _angle))
    {
      LOG_ERROR("设置零度角{}失败", _angle);
      return false;
    }
  }
  return true;
}

bool LidarManager::setMotorSpeed(const MotorSpeedType _motor_speed)
{
  return mech_communication_ptr_->writeCmd(mech::NET_CMD_CONFIG_SET_MOTOR_SPEED, static_cast<uint16_t>(_motor_speed));
}
bool LidarManager::stopMotor() { return setMotorSpeed(MotorSpeedType::MOTOR_SPEED_0); }

std::optional<mech::MountType> LidarManager::getMountType()
{
  if (!mech_communication_ptr_->isConnected())
  {
    LOG_INDEX_INFO("雷达未连接，无法获取安装方式");
    return std::nullopt;
  }
  if (!difop_packet_.isValid())
  {
    LOG_INDEX_ERROR("获取difop无效");
  }
  return difop_packet_.mount_type;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool LidarManager::writeTopFlash(const QString& _file_path, const uint32_t _addr_start, const uint32_t _addr_end)
{
  is_abort_ = false;
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }

  uint32_t data_size = file.size();

  // 封装尝试开始写入的方法
  auto try_start_write = [&]() -> bool {
    return mech_communication_ptr_->startWriteTopFlash(_addr_start, data_size, 8000);
  };

  // 确保成功开始写入，否则尝试结束并重启
  if (!try_start_write())
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    if ((!mech_communication_ptr_->finishWriteTopFlash()))
    {
      LOG_ERROR("结束写入TopFlash失败");
      return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    if (!try_start_write())
    {
      LOG_ERROR("开始写入TopFlash失败");
    }

    return false;
    LOG_ERROR("开始写入TopFlash失败");
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  // 每帧1024字节发送数据
  uint32_t pkt_count = 1;
  while (!file.atEnd())
  {
    if (is_abort_)
    {
      LOG_WARN("写入TopFlash被中断");
      return false;
    }

    QByteArray raw_data = file.read(1024);
    std::vector<uint8_t> data(raw_data.begin(), raw_data.end());

    if (!mech_communication_ptr_->writeTopFlash(pkt_count++, data))
    {
      LOG_WARN("写入TopFlash失败, pkt_count: {}", pkt_count);
      return false;
    }
    // std::this_thread::sleep_for(std::chrono::milliseconds(10));

    // 更新进度
    auto progress = 100 * static_cast<float>(file.pos()) / static_cast<float>(data_size);
    AppEvent::getInstance()->signalUpdateProgressWriteTopFlash(static_cast<int>(progress));
  }

  // 确保完成写入过程
  if (!mech_communication_ptr_->finishWriteTopFlash() && !mech_communication_ptr_->finishWriteTopFlash(8000))
  {
    LOG_ERROR("结束写入TopFlash失败");
    return false;
  }

  return true;
}

bool LidarManager::writeTopFlash(const QByteArray& _data, const uint32_t _addr_start)
{
  uint32_t data_size = _data.size();
  // 封装尝试开始写入的方法
  auto try_start_write = [&]() -> bool {
    return mech_communication_ptr_->startWriteTopFlash(_addr_start, data_size, 8000);
  };

  // 确保成功开始写入，否则尝试结束并重启
  if (!try_start_write())
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    if (!mech_communication_ptr_->finishWriteTopFlash())
    {
      LOG_ERROR("结束写入TopFlash失败");
      return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    if (!try_start_write())
    {
      LOG_ERROR("开始写入TopFlash失败");
      return false;
    }
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  // 每帧1024字节发送数据
  uint32_t pkt_count = 1;
  uint32_t offset    = 0;
  while (offset < data_size)
  {
    if (is_abort_)
    {
      LOG_WARN("写入TopFlash被中断");
      return false;
    }

    // 读取一帧数据
    QByteArray raw_data = _data.mid(offset, 1024);
    offset += raw_data.size();
    std::vector<uint8_t> data(raw_data.begin(), raw_data.end());

    if (!mech_communication_ptr_->writeTopFlash(pkt_count++, data))
    {
      LOG_WARN("写入TopFlash失败, pkt_count: {}", pkt_count);
      return false;
    }

    // 更新进度
    auto progress = 100 * static_cast<float>(offset) / static_cast<float>(data_size);
    AppEvent::getInstance()->signalUpdateProgressWriteTopFlash(static_cast<int>(progress));
  }

  // 确保完成写入过程
  if (!mech_communication_ptr_->finishWriteTopFlash() && !mech_communication_ptr_->finishWriteTopFlash(8000))
  {
    LOG_ERROR("结束写入TopFlash失败");
    return false;
  }

  LOG_INFO("写入TopFlash成功");
  return true;
}

bool LidarManager::writeReflBit(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  QByteArray file_data = file.readAll();  // 一次性读取文件内容
  file.close();
  ReflBit refl_bit {};
  // 读取文件到TwoDimAbsBit
  if (file_data.size() == sizeof(ReflBit))
  {
    std::memcpy(&refl_bit, file_data.data(), sizeof(ReflBit));
  }
  else
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path.toStdString());
    return false;
  }

  auto crc = CrcUtils::calcCrc(refl_bit.arr.data(), refl_bit.arr.size());
  file_data.append(static_cast<char>(crc & 0xFF));         // CRC低字节
  file_data.append(static_cast<char>((crc >> 8) & 0xFF));  // CRC高字节

  // 0xF00000
  CombineBit bit;
  uint32_t addr_start = 0xF00000 + bit.getReflOffset();
  return writeTopFlash(file_data, addr_start);
}
bool LidarManager::writeDynamicBit(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  QByteArray file_data = file.readAll();  // 一次性读取文件内容
  file.close();
  DynamicBit dynamic_bit {};
  // 读取文件到TwoDimAbsBit
  if (file_data.size() == sizeof(DynamicBit))
  {
    std::memcpy(&dynamic_bit, file_data.data(), sizeof(DynamicBit));
  }
  else
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path.toStdString());
    return false;
  }

  auto crc = CrcUtils::calcCrc(dynamic_bit.arr.data(), dynamic_bit.arr.size());
  file_data.append(static_cast<char>(crc & 0xFF));         // CRC低字节
  file_data.append(static_cast<char>((crc >> 8) & 0xFF));  // CRC高字节
  // 0xF00000
  CombineBit bit;
  uint32_t addr_start = 0xF00000 + bit.getDynamicOffset();
  return writeTopFlash(file_data, addr_start);
}
bool LidarManager::writeStaticBit(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  QByteArray file_data = file.readAll();  // 一次性读取文件内容
  file.close();
  StaticBit static_bit {};
  // 读取文件到TwoDimAbsBit
  if (file_data.size() == sizeof(StaticBit))
  {
    std::memcpy(&static_bit, file_data.data(), sizeof(StaticBit));
  }
  else
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path.toStdString());
    return false;
  }
  static_bit.toBigEndian();
  auto crc = CrcUtils::calcCrc(static_bit.arr.data(), static_bit.arr.size());
  file_data.append(static_cast<char>(crc & 0xFF));         // CRC低字节
  file_data.append(static_cast<char>((crc >> 8) & 0xFF));  // CRC高字节
  // 0xF00000
  CombineBit bit;
  uint32_t addr_start = 0xF00000 + bit.getStaticOffset();
  return writeTopFlash(file_data, addr_start);
}
bool LidarManager::writeTwoDimBit(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  QByteArray file_data = file.readAll();  // 一次性读取文件内容
  file.close();
  TwoDimAbsBit two_dim_abs_bit {};
  // 读取文件到TwoDimAbsBit
  if (file_data.size() == sizeof(TwoDimAbsBit))
  {
    std::memcpy(&two_dim_abs_bit, file_data.data(), sizeof(TwoDimAbsBit));
  }
  else
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path.toStdString());
    return false;
  }
  auto crc = CrcUtils::calcCrc(two_dim_abs_bit.arr.data(), two_dim_abs_bit.arr.size());
  file_data.append(static_cast<char>(crc & 0xFF));         // CRC低字节
  file_data.append(static_cast<char>((crc >> 8) & 0xFF));  // CRC高字节
  // 0xF00000
  CombineBit bit;
  uint32_t addr_start = 0xF00000 + bit.getTwoDimAbsOffset();
  return writeTopFlash(file_data, addr_start);
}
bool LidarManager::writeAbsBit(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  QByteArray file_data = file.readAll();  // 一次性读取文件内容
  file.close();

  AbsBit abs_bit {};
  // 读取文件到AbsBit
  if (file_data.size() == sizeof(AbsBit))
  {
    std::memcpy(&abs_bit, file_data.data(), sizeof(AbsBit));
  }
  else
  {
    LOG_ERROR("文件大小不正确，文件路径：{}", _file_path.toStdString());
    return false;
  }
  auto crc = CrcUtils::calcCrc(abs_bit.arr.data(), abs_bit.arr.size());
  file_data.append(static_cast<char>(crc & 0xFF));         // CRC低字节
  file_data.append(static_cast<char>((crc >> 8) & 0xFF));  // CRC高字节
  // 0xF00000
  CombineBit bit;
  uint32_t addr_start = 0xF00000 + bit.getAbsOffset();
  return writeTopFlash(file_data, addr_start);
}

bool LidarManager::LidarManager::writeVbdGdi(const uint32_t _vbd_intercept_hex, const uint32_t _vbd_err_hex)
{
  CustomGdiFeild custom_gdi_feild {};
  custom_gdi_feild.custom_gdi_reg.vbd_err.setData(0x8c3200, _vbd_err_hex);
  custom_gdi_feild.custom_gdi_reg.vbd_intercept.setData(0x8c3018, _vbd_intercept_hex);
  custom_gdi_feild.custom_gdi_reg.refl_calib_version_reg.setData(REF_CALIB_VERSION_REG_ADDR_START, 0x04);

  auto crc                 = CrcUtils::calcCrc(custom_gdi_feild.custom_gdi_reg.arr.data(), sizeof(CustomGdiReg));
  custom_gdi_feild.gdi_crc = crc;

  QByteArray data(custom_gdi_feild.arr.data(), sizeof(CustomGdiFeild));

  return writeTopFlash(data, CustomGdiFeild::getStartAddr());
}

bool LidarManager::setEyeSafe(const bool _is_open)
{
  if (!mech_communication_ptr_->writeCmd(mech::NET_CMD_TOP_BOARD_EYES_SAFE, static_cast<uint8_t>(_is_open)))
  {
    LOG_ERROR("关闭人眼安全失败");
    return false;
  }

  return true;
}

bool LidarManager::stopMotorToAngle(const double& _angle)
{

  double angle = _angle * 10;

  uint8_t motor_angle_high = static_cast<uint8_t>(angle / 256);
  uint8_t motor_angle_low  = static_cast<uint8_t>(static_cast<int>(angle) % 256);

  std::vector<uint8_t> data;
  data = { 0x29, motor_angle_high, motor_angle_low };

  if (!mech_communication_ptr_->writeCmd(mech::NET_CMD_MOTOR_SEND_CMD, data, 6000))
  {
    LOG_ERROR("发送停止电机命令失败");
    return false;
  }

  return true;
}
bool LidarManager::startMotor()
{
  std::vector<uint8_t> data;
  data = { 0x03 };

  return mech_communication_ptr_->writeCmd(mech::NET_CMD_MOTOR_SEND_CMD, data, 6000);
}

bool LidarManager::getConfigParam(mech::ConfigPara& _config_para)
{
  _config_para = {};
  if (!mech_communication_ptr_->readConfigParamater(_config_para))
  {
    LOG_ERROR("获取雷达参数失败");
    return false;
  }

  uint32_t config_version_addr = 0;
  config_version_              = 0;
  if (!getRegisterAddr("config_version", config_version_addr) || !readRegData(config_version_addr, config_version_))
  {
    LOG_ERROR("获取整机配置版本失败");
    return false;
  }

  LOG_INFO("获取雷达参数成功:");
  LOG_INFO("sn: {}", _config_para.getSn());
  LOG_INFO("顶板版本: {:#x}", _config_para.getPlVersion());
  LOG_INFO("底板版本: {:#x}", _config_para.getPsVersion());
  LOG_INFO("软件版本: {:#x}", _config_para.getSoftwareVersion());
  LOG_INFO("电机版本: {:#x}", _config_para.getMotorVersion());
  LOG_INFO("整机配置版本: {:#x}", config_version_);
  LOG_INFO("零度角: {}", _config_para.angle0);
  LOG_INFO("码盘标定状态: {}", _config_para.status_of_code_wheel_cali);
  return true;
}

LidarManager::Version LidarManager::getVersion()
{
  Version version {};
  version.pl_version     = config_para_.getPlVersion();
  version.ps_version     = config_para_.getPsVersion();
  version.app_version    = config_para_.getSoftwareVersion();
  version.motor_version  = config_para_.getMotorVersion();
  version.config_version = config_version_;
  return version;
}

template bool LidarManager::writeCmd<uint16_t>(const uint32_t _cmd_type, const uint16_t _value, const uint32_t _msec);
template bool LidarManager::writeCmd<float>(const uint32_t _cmd_type, const float _value, const uint32_t _msec);
template bool LidarManager::writeCmd<uint32_t>(const uint32_t _cmd_type, const uint32_t _value, const uint32_t _msec);
template bool LidarManager::writeCmd<int>(const uint32_t _cmd_type, const int _value, const uint32_t _msec);
template <typename T>
bool LidarManager::writeCmd(const uint32_t _cmd_type, const T _value, const uint32_t _msec)
{
  return mech_communication_ptr_->writeCmd(_cmd_type, _value, _msec);
}
bool LidarManager::writeCmd(const uint32_t _cmd_type, const std::vector<uint8_t>& _data, const uint32_t _msec)
{
  return mech_communication_ptr_->writeCmd(_cmd_type, _data, _msec);
}

template bool LidarManager::readCmd<uint16_t>(const uint32_t _cmd_type, uint16_t& _value, const uint32_t _msec);
template bool LidarManager::readCmd<float>(const uint32_t _cmd_type, float& _value, const uint32_t _msec);
template bool LidarManager::readCmd<uint32_t>(const uint32_t _cmd_type, uint32_t& _value, const uint32_t _msec);
template bool LidarManager::readCmd<int>(const uint32_t _cmd_type, int& _value, const uint32_t _msec);
template <typename T>
bool LidarManager::readCmd(const uint32_t _cmd_type, T& _value, const uint32_t _msec)
{
  return mech_communication_ptr_->readCmd(_cmd_type, _value, _msec);
}

bool LidarManager::autoSetLidarSN()
{
  if (!getConfigParam(config_para_))
  {
    return false;
  }

  getLidarInfo()->setLidarSN(config_para_.getSn().c_str());
  app()->getWidgetLogSetting()->setFirmwareRevision(getLidarInfo()->getLidarIndex(),
                                                    static_cast<int>(config_para_.getPlVersion()),
                                                    static_cast<int>(config_para_.getPsVersion()));
  return true;
}

bool LidarManager::writeChnAngle()
{
  std::vector<std::vector<float>> angle_vec_vec;
  if (app()->getLineNum() == "48")
  {
    LOG_INFO("雷达线数为48");
    angle_vec_vec = angle_vec_vec_48_;
  }
  else if (app()->getLineNum() == "96")
  {
    LOG_INFO("雷达线数为96");
    angle_vec_vec = angle_vec_vec_96_;
  }
  else
  {
    LOG_ERROR("未知雷达线数");
    return false;
  }
  std::vector<float> ver_chn_angle_vec(angle_vec_vec.size());
  std::vector<float> hor_chn_angle_vec(angle_vec_vec.size());
  for (std::size_t i = 0; i < angle_vec_vec.size(); ++i)
  {
    ver_chn_angle_vec.at(i) = angle_vec_vec.at(i).at(0);
    hor_chn_angle_vec.at(i) = angle_vec_vec.at(i).at(1);
  }

  if (!mech_communication_ptr_->writeChnAngle(ver_chn_angle_vec, hor_chn_angle_vec))
  {
    LOG_ERROR("写入通道角度失败");
    return false;
  }

  std::vector<float> read_ver_chn_angle_vec;
  std::vector<float> read_hor_chn_ange_vec;
  if (!mech_communication_ptr_->readChnAngle(read_ver_chn_angle_vec, read_hor_chn_ange_vec, ver_chn_angle_vec.size()))
  {
    LOG_ERROR("通道角读取失败");
    return false;
  }

  if (read_hor_chn_ange_vec != hor_chn_angle_vec || read_ver_chn_angle_vec != ver_chn_angle_vec)
  {
    LOG_ERROR("通道角回读校验失败, 读取垂直角度: {}, 水平角度: {}, 输入垂直角: {}, 水平角:{}", read_ver_chn_angle_vec,
              read_hor_chn_ange_vec, ver_chn_angle_vec, hor_chn_angle_vec);
    return false;
  }

  LOG_INFO("写入通道角度成功");
  return true;
}
bool LidarManager::readChnAngle(std::vector<float>& _ver_chn_angle_vec, std::vector<float>& _hor_chn_angle_vec)
{
  if (!mech_communication_ptr_->readChnAngle(_ver_chn_angle_vec, _hor_chn_angle_vec, 96))
  {
    return false;
  }
  ver_chn_angle_vec_ = _ver_chn_angle_vec;
  hor_chn_angle_vec_ = _hor_chn_angle_vec;
  return true;
}

bool LidarManager::writeVbdBias()
{
  uint8_t vbd_version = 0x01;
  auto reg_info       = reg_csv_utils_ptr_->getRegisterInfo("vbd_bias_version");
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取vbd版本寄存器失败");
    return false;
  }
  vbd_version = static_cast<uint8_t>(reg_info.value_at_calibration);
  return writeVbdBias(vbd_version);
}

bool LidarManager::writeVbdBias(uint8_t _version)
{
  auto vbd_key_name = fmt::format("vbd_bias_{:02x}", _version);
  auto reg_info     = reg_csv_utils_ptr_->getRegisterInfo(vbd_key_name);
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{:#02x}版本vbd截距寄存器失败", _version);
    return false;
  }
  if (!writeTopRegData(reg_info.address, static_cast<uint32_t>(reg_info.value_at_calibration), 3))
  {
    LOG_INDEX_ERROR("写入{:#02x}版本vbd截距寄存器失败", _version);
    return false;
  }
  LOG_INDEX_INFO("写入{:#02x}版本vbd截距寄存器成功", _version);
  return true;
}

bool LidarManager::readReg459Pair(const uint32_t _reg_addr, uint32_t& _reg_val)
{
  if (!mech_communication_ptr_->read459PairRegData(_reg_addr, _reg_val))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  return true;
}
bool LidarManager::readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2)
{
  // 获取vbd曲线的寄存器信息
  auto reg_info_v0 = reg_csv_utils_ptr_->getRegisterInfo("vbd_n40");
  auto reg_info_v1 = reg_csv_utils_ptr_->getRegisterInfo("vbd_85");
  auto reg_info_v2 = reg_csv_utils_ptr_->getRegisterInfo("vbd_125");
  if (!reg_info_v0.is_ok || !reg_info_v1.is_ok || !reg_info_v2.is_ok)
  {
    LOG_INDEX_ERROR("获取vbd曲线寄存器失败");
    return false;
  }

  const int MAX_ATTEMPTS = 5;  // 总共尝试5次
  for (int i = 0; i < MAX_ATTEMPTS; i++)
  {
    bool stable           = true;  // 标记本轮尝试中的3次读取是否完全一致
    uint16_t candidate_v0 = 0;
    uint16_t candidate_v1 = 0;
    uint16_t candidate_v2 = 0;

    // 连续读取3次
    for (int j = 0; j < 3; j++)
    {
      uint32_t temp_v0 = 0;
      uint32_t temp_v1 = 0;
      uint32_t temp_v2 = 0;
      if (!mech_communication_ptr_->read459PairRegData(reg_info_v0.address, temp_v0))
      {
        stable = false;
        break;
      }
      if (!mech_communication_ptr_->read459PairRegData(reg_info_v1.address, temp_v1))
      {
        stable = false;
        break;
      }
      if (!mech_communication_ptr_->read459PairRegData(reg_info_v2.address, temp_v2))
      {
        stable = false;
        break;
      }

      if (j == 0)
      {
        // 第一次读取，记录下候选值
        candidate_v0 = static_cast<uint16_t>(temp_v0);
        candidate_v1 = static_cast<uint16_t>(temp_v1);
        candidate_v2 = static_cast<uint16_t>(temp_v2);
      }
      else
      {
        // 后续读取与第一次结果进行对比
        if (candidate_v0 != static_cast<uint16_t>(temp_v0) || candidate_v1 != static_cast<uint16_t>(temp_v1) ||
            candidate_v2 != static_cast<uint16_t>(temp_v2))
        {
          stable = false;
          break;
        }
      }
    }

    if (stable)
    {
      // 若3次连续读取一致，返回成功
      _v0 = candidate_v0;
      _v1 = candidate_v1;
      _v2 = candidate_v2;
      return true;
    }
  }

  LOG_INDEX_ERROR("连续读取vbd曲线不稳定，经过12次尝试均未连续读取3次一致的结果");
  return false;
}

bool LidarManager::readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex)
{
  auto reg_info_vbd_intercept = reg_csv_utils_ptr_->getRegisterInfo("vbd_intercept");
  auto reg_info_vbd_err       = reg_csv_utils_ptr_->getRegisterInfo("vbd_err");
  if (!reg_info_vbd_intercept.is_ok || !reg_info_vbd_err.is_ok)
  {
    LOG_INDEX_ERROR("获取vbd寄存器失败");
    return false;
  }
  if (!readTopRegData(reg_info_vbd_intercept.address, _vbd_intercept_hex, 3))
  {
    LOG_INDEX_ERROR("读取vbd截距寄存器失败");
    return false;
  }
  if (!readTopRegData(reg_info_vbd_err.address, _vbd_err_hex, 2))
  {
    LOG_INDEX_ERROR("读取vbd误差寄存器失败");
    return false;
  }
  return true;
}

void LidarManager::abort() { mech_communication_ptr_->abort(); }
void LidarManager::abortWritingFlash() { is_abort_ = true; }
bool LidarManager::getRegisterAddr(const QString& _register_name, uint32_t& _register_addr)
{
  auto reg_info  = reg_csv_utils_ptr_->getRegisterInfo(_register_name.toStdString());
  _register_addr = reg_info.address;
  return reg_info.is_ok;
}

bool LidarManager::writeCsvData(const QString& _key)
{
  auto reg_info_map = reg_csv_utils_ptr_->getSelectIndexPropertyRegisterInfo(_key.toStdString());

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (!mech_communication_ptr_->writeRegData(reg_info.address, reg_info.value_at_calibration))
    {
      LOG_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_at_calibration);
      return false;
    }
    // std::this_thread::sleep_for(std::chrono::milliseconds(100));
    uint32_t reg_val = 0;
    if (!mech_communication_ptr_->readRegData(reg_info.address, reg_val))
    {
      LOG_INDEX_ERROR("读取[{0:}:{1:#x}]失败", reg_name, reg_info.address);
      return false;
    }
    if (reg_val == reg_info.value_at_calibration)
    {
      LOG_INDEX_INFO("写入[{0:}:{1:#x}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_at_calibration);
    }
    else
    {
      LOG_INDEX_ERROR("写入校验失败,[{}] addr: {:#x}, 写入值: {:#x}, 读取值: {:#x}", reg_info.extra_str_info.at(0),
                      reg_info.address, reg_info.value_at_calibration, reg_val);
      if (reg_info.address == 0x2160)
      {
        // 此寄存器无法做回读校验，先暂时屏蔽
        continue;
      }
      return false;
    }
  }
  return true;
}
bool LidarManager::writeCsvDataAfterCalib(const QString& _key)
{
  auto reg_info_map = reg_csv_utils_ptr_->getSelectIndexPropertyRegisterInfo(_key.toStdString());

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (!mech_communication_ptr_->writeRegData(reg_info.address, reg_info.value_after_calibration))
    {
      LOG_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_after_calibration);
      return false;
    }
    LOG_INFO("[{0:}:{1:#x}]写入[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_after_calibration);
  }
  return true;
}
bool LidarManager::writeCsvCmdData(const QString& _key)
{
  auto reg_info_map = reg_csv_utils_ptr_->getSelectIndexPropertyRegisterInfo(_key.toStdString());

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (!mech_communication_ptr_->writeCmd(static_cast<uint16_t>(reg_info.address),
                                           static_cast<uint16_t>(reg_info.value_at_calibration)))
    {
      LOG_ERROR("[{0:}:{1:#x}]写入CMD[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_at_calibration);
      return false;
    }
    LOG_INFO("[{0:}:{1:#x}]写入CMD[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_at_calibration);
  }
  return true;
}
bool LidarManager::writeCsvCmdDataAfterCalib(const QString& _key)
{
  auto reg_info_map = reg_csv_utils_ptr_->getSelectIndexPropertyRegisterInfo(_key.toStdString());

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    if (!mech_communication_ptr_->writeCmd(static_cast<uint16_t>(reg_info.address),
                                           static_cast<uint16_t>(reg_info.value_after_calibration)))
    {
      LOG_ERROR("[{0:}:{1:#x}]写入CMD[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_after_calibration);
      return false;
    }
    LOG_INFO("[{0:}:{1:#x}]写入CMD[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_after_calibration);
  }
  return true;
}

bool LidarManager::writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
{
  if (!mech_communication_ptr_->writeRegData(_reg_addr, _reg_val, _msec))
  {
    LOG_ERROR("[{0:#x}:{1:#x}]写入失败", _reg_addr, _reg_val);
    return false;
  }
  LOG_INFO("[{0:#x}:{1:#x}]写入成功", _reg_addr, _reg_val);
  return true;
}
bool LidarManager::readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  if (!mech_communication_ptr_->readRegData(_reg_addr, _reg_val, _msec))
  {
    LOG_ERROR("[{0:#x}]读取失败", _reg_addr);
    return false;
  }
  LOG_INFO("[{0:#x}:{1:#x}]读取成功", _reg_addr, _reg_val);
  return true;
}

bool LidarManager::readTopRegDataByKey(const QString& _key,
                                       uint32_t& _data,
                                       const uint32_t _byte_size,
                                       const int _timeout)
{
  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = reg_csv_utils_ptr_->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }

  if (_key.contains("cmd"))
  {
    if (!readCmd(reg_info.address, _data, _timeout))
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}] cmd读取失败", _key, reg_info.address);
      return false;
    }
  }

  return readTopRegData(reg_info.address, _data, _byte_size, _timeout);
}

bool LidarManager::writeTopRegDataByKey(const QString& _key,
                                        const uint32_t _data,
                                        // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                        const uint32_t _byte_size,
                                        const int _timeout)
{
  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = reg_csv_utils_ptr_->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }
  return writeTopRegData(reg_info.address, _data, _byte_size, _timeout);
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool LidarManager::writeTopRegData(const uint32_t _reg_addr,
                                   const uint32_t _reg_val,
                                   const uint32_t _byte_size,
                                   const uint32_t _msec)
{
  std::vector<uint32_t> data;
  data.reserve(_byte_size);
  for (size_t i = 0; i < _byte_size; ++i)
  {
    data.push_back((_reg_val >> ((_byte_size - 1 - i) * 8)) & 0xffU);
  }
  if (!mech_communication_ptr_->writeRegData(_reg_addr, data, _msec))
  {
    LOG_INDEX_ERROR("[{:#x}*{}:{:#x}] 写入失败", _reg_addr, _byte_size, data);
    return false;
  }
  LOG_INDEX_INFO("[{:#x}*{}:{:#x}] 写入成功", _reg_addr, _byte_size, fmt::join(data.begin(), data.end(), ","));
  return true;
}
bool LidarManager::readTopRegData(const uint32_t _reg_addr,
                                  uint32_t& _reg_val,
                                  const uint32_t _byte_size,
                                  const uint32_t _msec)
{
  std::vector<uint32_t> data;
  if (!mech_communication_ptr_->readRegData(_reg_addr, _byte_size, data, _msec))
  {
    LOG_INDEX_ERROR("[{:#x}*{}] 读取失败", _reg_addr, _byte_size);
    return false;
  }
  _reg_val = 0;
  for (auto& item : data)
  {
    _reg_val = (_reg_val << 8U) | (item & 0xffU);
  }
  LOG_INDEX_INFO("[{:#x}*{}:{:#x}] [{:#x}] 读取成功", _reg_addr, _byte_size, _reg_addr, _reg_val);
  return true;
}

float LidarManager::getChnHorizontalAngle(const uint32_t _chn_num)
{
  hor_chn_angle_vec_ = angle_vec_vec_96_.at(1);
  if (hor_chn_angle_vec_.empty())
  {
    LOG_INDEX_ERROR("获取通道角度失败，雷达线数为{}", app()->getLineNum());
    return 0.0F;
  }
  return hor_chn_angle_vec_.at(_chn_num - 1);
}

}  // namespace lidar
}  // namespace robosense
