﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "widget_lidar_info_list.h"
#include "app_event.h"
#include "config.h"
#include "rs_logger.h"
#include "widget_lidar_info.h"
#include <QGraphicsDropShadowEffect>
#include <QListWidget>
#include <QPushButton>

using AppEvent = robosense::lidar::AppEvent;
WidgetLidarInfoList::WidgetLidarInfoList(QWidget* _parent) : WidgetLidarInfoList(1, _parent) {}

WidgetLidarInfoList::WidgetLidarInfoList(const int _lidar_num, QWidget* _parent) :
  QWidget(_parent), layout_(new QVBoxLayout(this)), lidar_num_(_lidar_num)
{

  this->setLayout(layout_);

  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnecting, this, &WidgetLidarInfoList::slotConnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnected, this, &WidgetLidarInfoList::slotConnected);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnecting, this, &WidgetLidarInfoList::slotDisconnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnected, this, &WidgetLidarInfoList::slotDisconnected);
}

WidgetLidarInfoList::~WidgetLidarInfoList() = default;

void WidgetLidarInfoList::createLidarInfo()
{
  for (int i = 1; i <= lidar_num_; ++i)
  {
    createLidarInfo(i);
    auto widget_info = widget_info_map_[i];
    layout_->addWidget(widget_info.lidar);
    layout_->addWidget(widget_info.button_connect);
    layout_->addWidget(widget_info.button_open_data_folder);
  }
  updateLidarInfoList(lidar_num_);
}

void WidgetLidarInfoList::updateLidarInfoList(int _lidar_num) {}

void WidgetLidarInfoList::createLidarInfo(const uint32_t _index)
{
  WidgetLidarInfo* widget_lidar_info = new WidgetLidarInfo(PROJECT_NAME, _index, this);
  widget_info_map_[_index].lidar     = widget_lidar_info;

  widget_lidar_info->setLidarSNPos(0, 1);
  widget_lidar_info->setProjectCodePos(1, 1);
  widget_lidar_info->setIPPos(2, 1);
  widget_lidar_info->setMSOPPos(3, 1);
  widget_lidar_info->setDIFOPPos(4, 1);
  app()->getWidgetLogSetting()->registerWidgetLidarInfo(widget_lidar_info);
  WidgetLidarInfo::setNotCheckSNProject("0350");
  widget_lidar_info->setFixedLidarInstallPosition(rsfsc_lib::LIDAR_INSTALL_POSITION_FL);
  widget_lidar_info->setFixedProjectCode(rsfsc_lib::PROJECT_0350);
  QPushButton* button_connect             = new QPushButton("连接", this);
  widget_info_map_[_index].button_connect = button_connect;

  QPushButton* button_open_data_folder             = new QPushButton("打开数据文件夹", this);
  widget_info_map_[_index].button_open_data_folder = button_open_data_folder;

  connect(button_open_data_folder, &QPushButton::clicked,
          [this, _index]() { signalButtonOpenDataFolderClicked(_index); });

  connect(button_connect, &QPushButton::clicked, [this, _index]() {
    if (widget_info_map_[_index].button_connect->text() == "连接")
    {
      signalButtonConnectClicked(_index);
    }
    else
    {
      signalButtonDisconnectClicked(_index);
    }
  });
}

WidgetLidarInfo* WidgetLidarInfoList::getLidarInfo(const uint32_t _index)
{
  if (widget_info_map_.find(_index) != widget_info_map_.end())
  {
    return widget_info_map_[_index].lidar;
  }

  LOG_ERROR("lidar index {} not exist in list: ", _index);
  return nullptr;
}

void WidgetLidarInfoList::setLidarInfoState(const uint32_t _index, const bool _enable)
{
  if (widget_info_map_.find(_index) == widget_info_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list: ", _index);
    return;
  }

  widget_info_map_[_index].lidar->setEnabled(_enable);
}

void WidgetLidarInfoList::slotConnecting(const quint32 _index)
{
  setLidarInfoState(_index, false);
  widget_info_map_[_index].button_connect->setText("连接中");
  widget_info_map_[_index].button_connect->setEnabled(false);
}
void WidgetLidarInfoList::slotConnected(const quint32 _index)
{
  setLidarInfoState(_index, false);
  widget_info_map_[_index].button_connect->setText("断开连接");
  widget_info_map_[_index].button_connect->setEnabled(true);
}
void WidgetLidarInfoList::slotDisconnecting(const quint32 _index)
{
  setLidarInfoState(_index, false);
  widget_info_map_[_index].button_connect->setText("断开中");
  widget_info_map_[_index].button_connect->setEnabled(false);
}
void WidgetLidarInfoList::slotDisconnected(const quint32 _index)
{
  setLidarInfoState(_index, true);
  widget_info_map_[_index].button_connect->setText("连接");
  widget_info_map_[_index].button_connect->setEnabled(true);
}