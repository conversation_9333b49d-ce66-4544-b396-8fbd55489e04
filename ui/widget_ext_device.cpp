﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include <QSerialPortInfo>

#include "app_event.h"
#include "relay_controller_driver/include/relay_controller_driver/relay_controller_factory.h"
#include "rotator_driver/include/bocic_rotator_controller/bocic_rotator.h"
#include "rotator_driver/include/rotator_controller_factory.h"
#include "rotator_view/rotator_control_view.h"
#include "rs_logger.h"
#include "ui_widget_ext_device.h"
#include "widget_ext_device.h"
#include <QTimer>

using RotatorAxis = robosense::lidar::RotatorControllerInterface::RotatorAxis;
Q_DECLARE_METATYPE(std::shared_ptr<RotatorControllerInterface>);
using namespace robosense::lidar;

WidgetExtDevice::WidgetExtDevice(QWidget* _parent) :
  QWidget(_parent),
  ext_dev_ui_(new Ui::WidgetExtDevice),
  ext_device_worker_(new ExtDeviceWorker),
  ext_device_worker_thread_(new QThread(this))
{
  ext_dev_ui_->setupUi(this);
  app()->setWidgetExtDevice(this);

  ext_device_worker_thread_->setObjectName("ext_device_worker_thread");
  ext_device_worker_->moveToThread(ext_device_worker_thread_);
  ext_device_worker_thread_->start();

  for (const auto& ctrl_type : magic_enum::enum_names<RotatorControllerFactory::RotatorControllerType>())
  {
    ext_dev_ui_->combobox_rotator_controller_type->addItem(QString::fromStdString(std::string(ctrl_type)));
  }
  ext_dev_ui_->combobox_rotator_controller_type->setCurrentText(
    QString::fromStdString(fmt::format("{}", RotatorControllerFactory::RotatorControllerType::SC101)));
  for (const auto& rotator_type : magic_enum::enum_names<BocicRotator::BocicRotatorModel>())
  {
    ext_dev_ui_->combobox_rotator_type->addItem(QString::fromStdString(std::string(rotator_type)));
  }
  ext_dev_ui_->combobox_rotator_type->setCurrentText(
    QString::fromStdString(fmt::format("{}", BocicRotator::BocicRotatorModel::MRS102)));

  slotRefreshRotatorPortName();
  initSignalSlots();
}
WidgetExtDevice::~WidgetExtDevice()
{
  ext_device_worker_thread_->quit();
  ext_device_worker_thread_->wait();
  ext_device_worker_->deleteLater();
}
void WidgetExtDevice::initSignalSlots()
{
  connect(ext_dev_ui_->button_rotator_x_connect, &QPushButton::clicked, [this]() {
    if (ext_dev_ui_->button_rotator_x_connect->text() == "连接")
    {
      connectRotatorX();
      return;
    }
    disconnectRotatorX();
    ext_dev_ui_->button_rotator_x_connect->setText("连接");
  });
  connect(ext_dev_ui_->button_rotator_y_connect, &QPushButton::clicked, [this]() {
    if (ext_dev_ui_->button_rotator_y_connect->text() == "连接")
    {
      connectRotatorY();
      return;
    }
    disconnectRotatorY();
    ext_dev_ui_->button_rotator_y_connect->setText("连接");
  });
  // connect(ext_dev_ui_->button)
  connect(ext_dev_ui_->button_rotator_x_angle, &QPushButton::clicked,
          [this]() { setRotatorXAngle(static_cast<float>(ext_dev_ui_->spinbox_rotator_x_angle->value())); });
  connect(ext_dev_ui_->button_rotator_y_angle, &QPushButton::clicked,
          [this]() { setRotatorYAngle(static_cast<float>(ext_dev_ui_->spinbox_rotator_y_angle->value())); });
  connect(ext_dev_ui_->button_rotator_x_speed, &QPushButton::clicked,
          [this]() { setRotatorXSpeed(ext_dev_ui_->spinbox_rotator_x_speed->value()); });
  connect(ext_dev_ui_->button_rotator_y_speed, &QPushButton::clicked,
          [this]() { setRotatorYSpeed(ext_dev_ui_->spinbox_rotator_y_speed->value()); });
  connect(ext_dev_ui_->button_rotator_refresh_port, &QPushButton::clicked, this,
          &WidgetExtDevice::slotRefreshRotatorPortName);
  connect(ext_dev_ui_->button_rotator_x_reset_zero, &QPushButton::clicked, [this]() {
    OperationContext context;
    signalRotatorXResetZero(&context);
    context.wait();
  });
  connect(ext_dev_ui_->button_rotator_y_reset_zero, &QPushButton::clicked, [this]() {
    OperationContext context;
    signalRotatorYResetZero(&context);
    context.wait();
  });

  connect(ext_dev_ui_->button_relay_connect, &QPushButton::clicked, [this]() {
    if (ext_dev_ui_->button_relay_connect->text() == "连接")
    {
      connectRelay();
      return;
    }
    disconnectRelay();
    ext_dev_ui_->button_relay_connect->setText("连接");
  });
  connect(ext_dev_ui_->button_turn_off_relay, &QPushButton::clicked, this, &WidgetExtDevice::slotRelayTurnOff);
  connect(ext_dev_ui_->button_turn_on_relay, &QPushButton::clicked, this, &WidgetExtDevice::slotRelayTurnOn);

  connect(this, &WidgetExtDevice::signalSetRotatorXAngle, ext_device_worker_, &ExtDeviceWorker::slotSetRotatorXAngle,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalSetRotatorYAngle, ext_device_worker_, &ExtDeviceWorker::slotSetRotatorYAngle,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalSetRotatorXSpeed, ext_device_worker_, &ExtDeviceWorker::slotSetRotatorXSpeed,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalSetRotatorYSpeed, ext_device_worker_, &ExtDeviceWorker::slotSetRotatorYSpeed,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalIsRotatorXMoving, ext_device_worker_, &ExtDeviceWorker::slotIsRotatorXMoving,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalGetRotatorXAngle, ext_device_worker_, &ExtDeviceWorker::slotGetRotatorXAngle,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalConnectRotatorX, ext_device_worker_, &ExtDeviceWorker::slotConnectRotatorX,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalDisconnectRotatorX, ext_device_worker_,
          &ExtDeviceWorker::slotDisconnectRotatorX, Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalConnectRotatorY, ext_device_worker_, &ExtDeviceWorker::slotConnectRotatorY,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalDisconnectRotatorY, ext_device_worker_,
          &ExtDeviceWorker::slotDisconnectRotatorY, Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalRotatorXResetZero, ext_device_worker_, &ExtDeviceWorker::slotRotatorXResetZero,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalRotatorYResetZero, ext_device_worker_, &ExtDeviceWorker::slotRotatorYResetZero,
          Qt::QueuedConnection);

  connect(this, &WidgetExtDevice::signalConnectRelay, ext_device_worker_, &ExtDeviceWorker::slotConnectRelay,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalDisconnectRelay, ext_device_worker_, &ExtDeviceWorker::slotDisconnectRelay,
          Qt::QueuedConnection);
  connect(this, &WidgetExtDevice::signalTurnRelay, ext_device_worker_, &ExtDeviceWorker::slotTurnRelay,
          Qt::QueuedConnection);

  connect(ext_device_worker_, &ExtDeviceWorker::signalUpdateRotatorXAngle, this,
          &WidgetExtDevice::signalUpdateRotatorXAngle, Qt::QueuedConnection);
}

void WidgetExtDevice::init()
{
  QTimer::singleShot(0, [this]() {
    checkRotatorXConnection();
    connectRelay();
  });
}

void WidgetExtDevice::slotRefreshRotatorPortName()
{
  auto port_infos = QSerialPortInfo::availablePorts();
  ext_dev_ui_->combo_rotator_x_name->clear();
  ext_dev_ui_->combo_rotator_y_name->clear();
  ext_dev_ui_->combobox_relay_port_name->clear();
  for (const auto& port_info : port_infos)
  {
    ext_dev_ui_->combo_rotator_x_name->addItem(port_info.portName());
    ext_dev_ui_->combo_rotator_y_name->addItem(port_info.portName());
    ext_dev_ui_->combobox_relay_port_name->addItem(port_info.portName());

    // 打印串口的各种信息
    LOG_INFO("port_name: {}, description: {}, manufacturer: {}, serial_number: {}, system_location: {}, vendor_id: {}, "
             "product_id: {}",
             port_info.portName(), port_info.description(), port_info.manufacturer(), port_info.serialNumber(),
             port_info.systemLocation(), port_info.vendorIdentifier(), port_info.productIdentifier());
  }
}

void WidgetExtDevice::slotRelayTurnOff() { turnRelay(ext_dev_ui_->spinbox_relay_chn->value(), false); }
void WidgetExtDevice::slotRelayTurnOn() { turnRelay(ext_dev_ui_->spinbox_relay_chn->value(), true); }

bool WidgetExtDevice::checkRotatorXConnection()
{
  if (!ext_device_worker_->isRotatorXConnected() && !connectRotatorX())
  {
    LOG_ERROR("转台X无法连接");
    return false;
  }
  return true;
}
bool WidgetExtDevice::checkRotatorYConnection()
{
  if (!ext_device_worker_->isRotatorYConnected() && !connectRotatorY())
  {
    LOG_ERROR("转台Y无法连接");
    return false;
  }
  return true;
}

bool WidgetExtDevice::setRotatorXAngle(const float _angle)
{
  if (!checkRotatorXConnection())
  {
    return false;
  }
  OperationContext context;

  signalSetRotatorXAngle(_angle, &context);

  context.wait();

  return context.success;
}
bool WidgetExtDevice::setRotatorYAngle(const float _angle)
{
  if (!checkRotatorYConnection())
  {
    return false;
  }
  OperationContext context;

  signalSetRotatorYAngle(_angle, &context);

  context.wait();

  return context.success;
}
bool WidgetExtDevice::setRotatorXSpeed(const int _speed)
{
  if (!checkRotatorXConnection())
  {
    return false;
  }
  OperationContext context;

  signalSetRotatorXSpeed(_speed, &context);

  context.wait();

  return context.success;
}
bool WidgetExtDevice::setRotatorYSpeed(const int _speed)
{
  if (!checkRotatorYConnection())
  {
    return false;
  }
  OperationContext context;

  signalSetRotatorYSpeed(_speed, &context);

  context.wait();

  return context.success;
}
bool WidgetExtDevice::isRotatorXMoving(bool& _is_moving)
{
  if (!checkRotatorXConnection())
  {
    return false;
  }
  OperationContext context;

  signalIsRotatorXMoving(&context);

  context.wait();

  if (context.result.isValid())
  {
    _is_moving = context.result.toBool();
  }

  return context.success;
}
bool WidgetExtDevice::getRotatorXAngle(float& _angle)
{
  if (!checkRotatorXConnection())
  {
    return false;
  }
  if (!checkRotatorXConnection())
  {
    return false;
  }
  OperationContext context;

  signalGetRotatorXAngle(&context);

  context.wait();

  if (context.result.isValid())
  {
    _angle = context.result.toFloat();
  }

  return context.success;
}
bool WidgetExtDevice::connectRotatorX()
{
  OperationContext context;

  signalConnectRotatorX(ext_dev_ui_->combo_rotator_x_name->currentText(),
                        ext_dev_ui_->combobox_rotator_controller_type->currentText(),
                        ext_dev_ui_->combobox_rotator_type->currentText(), &context);

  context.wait();

  if (context.success)
  {
    ext_dev_ui_->button_rotator_x_connect->setText("断开");
  }
  else
  {
    app()->signalShowErrorText(
      fmt::format("转台打开失败, 串口名: {}", ext_dev_ui_->combo_rotator_x_name->currentText()).c_str());
  }

  return context.success;
}

void WidgetExtDevice::disconnectRotatorX()
{
  OperationContext context;

  signalDisconnectRotatorX(&context);

  context.wait();
}
bool WidgetExtDevice::connectRotatorY()
{
  OperationContext context;

  signalConnectRotatorY(ext_dev_ui_->combo_rotator_y_name->currentText(),
                        ext_dev_ui_->combobox_rotator_controller_type->currentText(),
                        ext_dev_ui_->combobox_rotator_type->currentText(), &context);

  context.wait();

  if (context.success)
  {
    ext_dev_ui_->button_rotator_y_connect->setText("断开");
  }
  else
  {
    app()->signalShowErrorText(
      fmt::format("转台打开失败, 串口名: {}", ext_dev_ui_->combo_rotator_x_name->currentText()).c_str());
  }

  return context.success;
}
void WidgetExtDevice::disconnectRotatorY()
{
  OperationContext context;

  signalDisconnectRotatorY(&context);

  context.wait();
}

bool WidgetExtDevice::connectRelay()
{
  OperationContext context;

  signalConnectRelay(ext_dev_ui_->combobox_relay_port_name->currentText(), &context);

  context.wait();

  if (context.success)
  {
    ext_dev_ui_->button_relay_connect->setText("断开");
  }

  return context.success;
}
void WidgetExtDevice::disconnectRelay()
{
  OperationContext context;

  signalDisconnectRelay(&context);

  context.wait();
}
bool WidgetExtDevice::turnRelay(const int _chn, const bool _is_open)
{
  OperationContext context;

  signalTurnRelay(_chn, _is_open, &context);

  context.wait();

  if (context.success)
  {
    if (_is_open)
    {
      app()->signalRelayTurnOn();
    }
    else
    {
      app()->signalRelayTurnOff();
    }
  }

  return context.success;
}

ExtDeviceWorker::ExtDeviceWorker(QObject* _parent) : QObject(_parent) {};
ExtDeviceWorker::~ExtDeviceWorker() = default;

std::shared_ptr<RotatorControllerInterface> ExtDeviceWorker::connectRotator(
  const QString& _port_name,
  const RotatorControllerFactory::RotatorControllerType& _rotator_ctrl_type,
  const std::string& _rotator_motor_type)
{
  const QString& port_name = _port_name;
  quint32 rotator_ctrl_type_uint {};
  int rotator_speed = 0;

  RSFSCLog::getInstance()->info(
    "connect rotator, port_name: {}, rotator_ctrl_type: {}, rotator_motor_type: {}, rotator_speed: {}",
    port_name.toStdString(), _rotator_ctrl_type, _rotator_motor_type, rotator_speed);

  std::shared_ptr<RotatorControllerInterface> rotator_ptr;

  rotator_ptr.reset(RotatorControllerFactory::createRotatorController(_rotator_ctrl_type, { port_name.toStdString() }));

  if (rotator_ptr == nullptr)
  {
    LOG_ERROR("不支持的控制器类型 {}", _rotator_ctrl_type);
    return nullptr;
  }

  if (!rotator_ptr->connect())
  {
    LOG_ERROR("转台{}连接失败", port_name);
    return nullptr;
  }
  LOG_INFO("串口{} 打开成功，控制器类型：{}", port_name, _rotator_ctrl_type);

  if (_rotator_ctrl_type == RotatorControllerFactory::RotatorControllerType::ASD ||
      _rotator_ctrl_type == RotatorControllerFactory::RotatorControllerType::CL_01A)
  {
    // if (!rotator_ptr->setRotatorSpeed(10000, RotatorAxis::X))
    // {
    //   LOG_INFO("设置转台速度失败");
    //   return nullptr;
    // }
    // if (!rotator_ptr->resetZeroPosition(RotatorAxis::X))
    // {
    //   LOG_INFO("转台回零点失败");
    //   return nullptr;
    // }
    return rotator_ptr;
  }
  if (!rotator_ptr->addRotator(RotatorAxis::X, _rotator_motor_type, true))
  {
    // RSFSCLog::getInstance()->error("添加X电机失败");
    LOG_ERROR("串口{} 添加X电机{}失败", port_name, _rotator_motor_type);
    return nullptr;
  }

  if (!rotator_ptr->setSoftLimit(0, 1080, RotatorAxis::X))
  {
    LOG_ERROR("关闭软限位失败");
    return nullptr;
  }

  return rotator_ptr;
}

bool ExtDeviceWorker::isRotatorXConnected() { return rotator_x_ptr_ != nullptr; }
bool ExtDeviceWorker::isRotatorYConnected() { return rotator_y_ptr_ != nullptr; }

void ExtDeviceWorker::slotSetRotatorXAngle(const float _angle, OperationContext* _context)
{
  ContextGuard guard(_context);

  if (!rotator_x_ptr_->rotateAngle(_angle, RotatorControllerInterface::RotatorAxis::X))
  {
    LOG_ERROR("转台X旋转{}失败", _angle);
    return;
  }
  LOG_INFO("转台X旋转{}°成功", _angle);
  _context->success = true;
}
void ExtDeviceWorker::slotSetRotatorYAngle(const float _angle, OperationContext* _context)
{
  ContextGuard guard(_context);

  if (!rotator_y_ptr_->rotateAngle(_angle, RotatorControllerInterface::RotatorAxis::X))
  {
    LOG_ERROR("转台Y旋转{}°失败", _angle);
    return;
  }
  _context->success = true;
}
void ExtDeviceWorker::slotSetRotatorXSpeed(const int _speed, OperationContext* _context)
{
  ContextGuard guard(_context);

  _context->success = rotator_x_ptr_->setRotatorSpeed(_speed, RotatorControllerInterface::RotatorAxis::X);
}
void ExtDeviceWorker::slotSetRotatorYSpeed(const int _speed, OperationContext* _context)
{
  ContextGuard guard(_context);
  _context->success = rotator_y_ptr_->setRotatorSpeed(_speed, RotatorControllerInterface::RotatorAxis::X);
}
void ExtDeviceWorker::slotIsRotatorXMoving(OperationContext* _context)
{
  ContextGuard guard(_context);

  bool is_moving    = false;
  _context->success = rotator_x_ptr_->isMoving(is_moving, RotatorControllerInterface::RotatorAxis::X);
  _context->result  = is_moving;
}
void ExtDeviceWorker::slotGetRotatorXAngle(OperationContext* _context)
{
  ContextGuard guard(_context);

  float angle = 0.0;
  if (!rotator_x_ptr_->getCurrentAngle(angle, RotatorControllerInterface::RotatorAxis::X))
  {
    return;
  }

  signalUpdateRotatorXAngle(angle);
  _context->result.setValue(angle);
  _context->success = true;
}
void ExtDeviceWorker::slotConnectRotatorX(const QString& _rotator_name,
                                          const QString& _ctrl_type,
                                          const QString& _rotator_type,
                                          OperationContext* _context)
{
  ContextGuard guard(_context);

  RotatorControllerFactory::RotatorControllerType rotator_ctrl_type =
    magic_enum::enum_cast<RotatorControllerFactory::RotatorControllerType>(_ctrl_type.toStdString()).value();
  rotator_x_ptr_ = connectRotator(_rotator_name, rotator_ctrl_type, _rotator_type.toStdString());
  if (rotator_x_ptr_ == nullptr)
  {
    LOG_ERROR("转台X无法连接");
    return;
  }

  _context->result.setValue(rotator_x_ptr_);
  _context->success = true;
}
void ExtDeviceWorker::slotDisconnectRotatorX(OperationContext* _context)
{
  ContextGuard guard(_context);

  if (rotator_x_ptr_ != nullptr)
  {
    rotator_x_ptr_->disconnect();
    rotator_x_ptr_.reset();
  }
}
void ExtDeviceWorker::slotConnectRotatorY(const QString& _rotator_name,
                                          const QString& _ctrl_type,
                                          const QString& _rotator_type,
                                          OperationContext* _context)
{
  ContextGuard guard(_context);

  RotatorControllerFactory::RotatorControllerType rotator_ctrl_type =
    magic_enum::enum_cast<RotatorControllerFactory::RotatorControllerType>(_ctrl_type.toStdString()).value();
  rotator_y_ptr_ = connectRotator(_rotator_name, rotator_ctrl_type, _rotator_type.toStdString());
  if (rotator_y_ptr_ == nullptr)
  {
    return;
  }
  _context->result.setValue(rotator_y_ptr_);
  _context->success = true;
}
void ExtDeviceWorker::slotDisconnectRotatorY(OperationContext* _context)
{
  ContextGuard guard(_context);

  if (rotator_y_ptr_ != nullptr)
  {
    rotator_y_ptr_->disconnect();
    rotator_y_ptr_.reset();
  }
}
void ExtDeviceWorker::slotRotatorXResetZero(OperationContext* _context)
{
  ContextGuard guard(_context);

  if (rotator_x_ptr_ == nullptr)
  {
    return;
  }
  _context->success = rotator_x_ptr_->resetZeroPosition(RotatorAxis::X);
}
void ExtDeviceWorker::slotRotatorYResetZero(OperationContext* _context)
{
  ContextGuard guard(_context);

  if (rotator_y_ptr_ == nullptr)
  {
    return;
  }
  _context->success = rotator_y_ptr_->resetZeroPosition(RotatorAxis::X);
}
void ExtDeviceWorker::slotConnectRelay(const QString& _port_name, OperationContext* _context)
{
  ContextGuard guard(_context);

  auto relay_type = RelayControllerFactory::RelayControllerType::ZHONG_KAI_CONTROLLER_8;

  auto* factory = RelayControllerFactory::createRelayController(relay_type, { _port_name.toStdString() });

  if (factory == nullptr)
  {
    LOG_ERROR("不支持的继电器类型 {}", relay_type);
    return;
  }

  relay_ptr_ = std::unique_ptr<RelayControllerInterface>(factory);
  if (relay_ptr_ == nullptr)
  {
    LOG_ERROR("继电器{}连接失败", _port_name);
    return;
  }
  std::string err_msg;
  relay_ptr_->connectController(err_msg);
  if (!err_msg.empty())
  {
    LOG_ERROR("继电器{}连接失败: {}", _port_name, err_msg);
    return;
  }
  LOG_INFO("继电器{}连接成功", _port_name);
  _context->success = true;
}
void ExtDeviceWorker::slotDisconnectRelay(OperationContext* _context)
{
  ContextGuard guard(_context);

  if (relay_ptr_ != nullptr)
  {
    relay_ptr_.reset();
  }
  _context->success = true;
}
void ExtDeviceWorker::slotTurnRelay(const int _chn, const bool _is_open, OperationContext* _context)
{
  ContextGuard guard(_context);

  if (relay_ptr_ == nullptr)
  {
    return;
  }

  std::string err_msg;
  if (_is_open)
  {
    relay_ptr_->turnOnChannel(_chn, err_msg);
  }
  else
  {
    relay_ptr_->turnOffChannel(_chn, err_msg);
  }
  if (!err_msg.empty())
  {
    LOG_ERROR("继电器操作{}失败: {}", _is_open, err_msg);
    return;
  }
  _context->success = true;
}
