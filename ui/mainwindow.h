﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "camera_window.h"
#include "common_struct.h"
#include "model/airy_wave_signal_model.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLabel>
#include <QList>
#include <QMainWindow>
#include <QPushButton>
#include <QSpinBox>

QT_BEGIN_NAMESPACE
namespace Ui
{
class MainWindow;
}  // namespace Ui
QT_END_NAMESPACE

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
class AppController;
class AppEvent;
namespace rsfsc_lib
{
class UserAuthority;
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  friend class robosense::lidar::AppController;
  friend class robosense::lidar::AppEvent;

  // 定义参数页面控件结构体
  struct ParaPageWidgets
  {
    QLabel* label_sampling_interval { nullptr };
    QSpinBox* spinbox_sampling_interval { nullptr };
    QLabel* label_factory_loc { nullptr };
    QComboBox* combobox_factory_loc { nullptr };
    QLabel* label_line_num { nullptr };
    QComboBox* combobox_line_num { nullptr };
    QPushButton* button_reload_config_para { nullptr };
    QPushButton* button_save_para { nullptr };
    QLabel* label_eth_name { nullptr };
    QLineEdit* lineedit_eth_name { nullptr };
    QPushButton* button_auto_get_eth { nullptr };
  };

  explicit MainWindow(QWidget* _parent = nullptr);
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  MainWindow(MainWindow&&)                 = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  ~MainWindow() override;

  enum class MessageType
  {
    INFO,
    WARNING,
    ERROR
  };

  void setModelMap(const std::map<uint32_t, std::shared_ptr<robosense::lidar::AiryWaveSignalModel>>& _model_map)
  {
    model_map_ = _model_map;
  }

protected:
  void closeEvent(QCloseEvent* _event) override;
  void readSettings();
  void writeSettings();

public Q_SLOTS:
  void slotButtonMainPageClicked();
  void slotButtonDevToolsClicked();
  void slotButtonParamClicked();
  void slotButtonDataProcessClicked();
  void slotButtonFunctionClicked();
  void slotButtonMesSettingClicked();

  void slotButtonSelectProcessPcapClicked();
  void slotButtonZeroPathClicked();

  void slotButtonCompareClicked();

  void slotButtonSelectBitFileClicked();
  void slotButtonWriteReflClicked();
  void slotButtonWriteDynamicClicked();
  void slotButtonWriteStaticClicked();
  void slotButtonWriteTwoAbsClicked();
  void slotButtonWriteAbsClicked();

  void slotButtonLoadVbdFileClicked();

  void slotFsmStarting(const int _index);
  void slotFsmStarted(const int _index);
  void slotFsmStopping(const int _index);
  void slotFsmStopped(const int _index);

  // lidar connection
  void slotLidarConnecting(const int _lidar_index);
  void slotLidarConnected(const int _lidar_index);
  void slotLidarDisconnecting(const int _lidar_index);
  void slotLidarDisconnected(const int _lidar_index);

  // progress
  void slotUpdateLabelRotatorXAngle(const float _angle);
  void slotUpdateZeroAngle(const float _angle, const bool _test);

  // void slotCheckBoxChnCtrl(const int _chn, const bool _is_checked);

  void slotShowInfoText(const QString& _msg);
  void slotShowWarningText(const QString& _msg);
  void slotShowErrorText(const QString& _msg);
  void slotShowErrorMsg(const int _error_type);
  void slotShowInfoVariant(const QString& _name, const QVariant& _value);

  void slotMsgBoxConfirm(const QString& _msg, robosense::lidar::MsgResult* _msg_result);
  void slotMsgBoxConfirmSpotAngle(robosense::lidar::MsgResult* _msg_result);

  void slotUpdateAuthority(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority);

Q_SIGNALS:
  void signalUpdatePageCurrentIndex(int _index);

  void signalStopMotor();

  void signalLoadVbdFile();

public:
  static void loadCsvAndJson();

private:
  void initMesWidget();
  void initWidgets();
  void initSignalSlots();
  static void loadJson(const QString& _root_path);
  static void loadCsv(const QString& _root_path);

  void setupUiParaPage();

  void showNonBlockingMessage(const QString& _msg, MessageType _type);

  Ui::MainWindow* ui_;
  ParaPageWidgets para_widgets_;

  std::vector<QCheckBox*> chn_ctrl_group_1_;
  std::vector<QCheckBox*> chn_ctrl_group_2_;
  std::vector<QCheckBox*> chn_ctrl_group_3_;

  QList<QWidget*> authority_widget_list_;

  std::map<uint32_t, std::shared_ptr<robosense::lidar::AiryWaveSignalModel>> model_map_;

  robosense::lidar::CameraWindow* camera_window_;
};
#endif  // MAINWINDOW_H
